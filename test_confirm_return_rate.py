#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试确认操作后的退货率计算
模拟确认操作的完整流程
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.return_rate_calculator import ReturnRateCalculator


def test_confirm_flow():
    """测试确认流程中的退货率计算"""
    print("🧪 测试确认操作后的退货率计算")
    print("=" * 60)
    
    # 模拟确认前的SKU数据（从ERP查询得到）
    original_skus = [
        {
            "sku_id": "GD653-6258-薄荷绿-S",
            "name": "时尚女装连衣裙 春季新款",
            "created": "2024-01-15 10:00:00",
            "sent_qty_15": 20,
            "as_qty_15": 8,
            "sent_qty_30": 25,
            "as_qty_30": 10,
            "cost_price": 45.0,
            "sale_price": 89.0
        },
        {
            "sku_id": "GD653-6258-薄荷绿-M",
            "name": "时尚女装连衣裙 春季新款",
            "created": "2024-01-15 10:05:00",
            "sent_qty_15": 18,
            "as_qty_15": 7,
            "sent_qty_30": 22,
            "as_qty_30": 9,
            "cost_price": 45.0,
            "sale_price": 89.0
        },
        {
            "sku_id": "GD653-6258-黑色-S",
            "name": "时尚女装连衣裙 春季新款 优质面料",
            "created": "2024-01-25 14:00:00",
            "sent_qty_15": 15,
            "as_qty_15": 6,
            "sent_qty_30": 18,
            "as_qty_30": 7,
            "cost_price": 48.0,
            "sale_price": 95.0
        },
        {
            "sku_id": "GD653-6258-黑色-M",
            "name": "时尚女装连衣裙 春季新款 优质面料",
            "created": "2024-01-25 14:10:00",
            "sent_qty_15": 12,
            "as_qty_15": 5,
            "sent_qty_30": 16,
            "as_qty_30": 6,
            "cost_price": 48.0,
            "sale_price": 95.0
        }
    ]
    
    print(f"📊 原始SKU数据：{len(original_skus)} 个")
    
    # 步骤1：计算确认前的退货率
    print("\n🔍 步骤1：确认前退货率计算")
    calculator = ReturnRateCalculator()
    
    # 按款号计算（旧方法）
    old_rates = calculator.calculate_return_rates_by_sku_code(original_skus)
    old_formatted = calculator.format_return_rate_integer(old_rates)
    print(f"  旧方法（按款号）: 15天={old_formatted['15天']} 30天={old_formatted['30天']}")
    
    # 按商品链接计算（新方法）
    link_rates = calculator.calculate_return_rates_by_product_links(original_skus)
    new_formatted = calculator.format_multi_link_display(link_rates)
    print(f"  新方法（按链接）: 15天={new_formatted['15天']} 30天={new_formatted['30天']}")
    
    # 步骤2：模拟用户选择部分颜色确认
    print("\n🎯 步骤2：用户选择薄荷绿颜色确认")
    selected_skus = [sku for sku in original_skus if "薄荷绿" in sku["sku_id"]]
    print(f"  选中的SKU：{len(selected_skus)} 个")
    for sku in selected_skus:
        print(f"    - {sku['sku_id']}")
    
    # 步骤3：计算确认后的退货率
    print("\n📊 步骤3：确认后退货率计算")
    
    # 使用选中的SKU计算退货率
    confirm_link_rates = calculator.calculate_return_rates_by_product_links(selected_skus)
    confirm_formatted = calculator.format_multi_link_display(confirm_link_rates)
    print(f"  确认后退货率: 15天={confirm_formatted['15天']} 30天={confirm_formatted['30天']}")
    
    # 检查是否出现"-"
    if confirm_formatted['15天'] == "-" or confirm_formatted['30天'] == "-":
        print("❌ 发现问题：确认后退货率变成了'-'")
        
        # 详细分析
        print("\n🔍 详细分析：")
        print(f"  选中SKU的退货率数据：")
        for sku in selected_skus:
            print(f"    {sku['sku_id']}:")
            print(f"      15天: 发货{sku['sent_qty_15']}件, 退货{sku['as_qty_15']}件")
            print(f"      30天: 发货{sku['sent_qty_30']}件, 退货{sku['as_qty_30']}件")
        
        # 检查聚合数据
        aggregated = calculator._aggregate_skus_by_code(selected_skus)
        print(f"  聚合后数据: {aggregated}")
        
        # 检查商品链接识别
        print(f"  商品链接识别结果: {len(confirm_link_rates)} 个链接")
        for link_id, rates in confirm_link_rates.items():
            print(f"    {link_id}: {rates}")
    else:
        print("✅ 确认后退货率计算正常")
    
    return confirm_formatted


def test_edge_cases():
    """测试边缘情况"""
    print("\n🧪 测试边缘情况")
    print("=" * 60)
    
    calculator = ReturnRateCalculator()
    
    # 测试1：空SKU列表
    print("🔍 测试1：空SKU列表")
    empty_rates = calculator.calculate_return_rates_by_product_links([])
    empty_formatted = calculator.format_multi_link_display(empty_rates)
    print(f"  结果: 15天={empty_formatted['15天']} 30天={empty_formatted['30天']}")
    
    # 测试2：单个SKU
    print("\n🔍 测试2：单个SKU")
    single_sku = [{
        "sku_id": "GD653-6258-薄荷绿-S",
        "name": "时尚女装连衣裙 春季新款",
        "created": "2024-01-15 10:00:00",
        "sent_qty_15": 20,
        "as_qty_15": 8,
        "sent_qty_30": 25,
        "as_qty_30": 10,
    }]
    single_rates = calculator.calculate_return_rates_by_product_links(single_sku)
    single_formatted = calculator.format_multi_link_display(single_rates)
    print(f"  结果: 15天={single_formatted['15天']} 30天={single_formatted['30天']}")
    
    # 测试3：没有退货率数据的SKU
    print("\n🔍 测试3：没有退货率数据的SKU")
    no_data_sku = [{
        "sku_id": "GD653-6258-薄荷绿-S",
        "name": "时尚女装连衣裙 春季新款",
        "created": "2024-01-15 10:00:00",
        "cost_price": 45.0,
        "sale_price": 89.0
        # 没有sent_qty_15等字段
    }]
    no_data_rates = calculator.calculate_return_rates_by_product_links(no_data_sku)
    no_data_formatted = calculator.format_multi_link_display(no_data_rates)
    print(f"  结果: 15天={no_data_formatted['15天']} 30天={no_data_formatted['30天']}")


def test_field_names():
    """测试不同字段名的兼容性"""
    print("\n🧪 测试字段名兼容性")
    print("=" * 60)
    
    calculator = ReturnRateCalculator()
    
    # 测试不同的字段名组合
    test_cases = [
        {
            "name": "标准字段名",
            "sku": {
                "sku_id": "GD653-6258-薄荷绿-S",
                "name": "时尚女装连衣裙 春季新款",
                "created": "2024-01-15 10:00:00",
                "sent_qty_15": 20,
                "as_qty_15": 8,
                "sent_qty_30": 25,
                "as_qty_30": 10,
            }
        },
        {
            "name": "中文字段名",
            "sku": {
                "sku_id": "GD653-6258-薄荷绿-S",
                "商品标题": "时尚女装连衣裙 春季新款",
                "创建时间": "2024-01-15 10:00:00",
                "sent_qty_15": 20,
                "as_qty_15": 8,
                "sent_qty_30": 25,
                "as_qty_30": 10,
            }
        },
        {
            "name": "混合字段名",
            "sku": {
                "sku_id": "GD653-6258-薄荷绿-S",
                "item_name": "时尚女装连衣裙 春季新款",
                "modified": "2024-01-15 10:00:00",
                "sent_qty_15": 20,
                "as_qty_15": 8,
                "sent_qty_30": 25,
                "as_qty_30": 10,
            }
        }
    ]
    
    for case in test_cases:
        print(f"\n🔍 测试 {case['name']}")
        rates = calculator.calculate_return_rates_by_product_links([case['sku']])
        formatted = calculator.format_multi_link_display(rates)
        print(f"  结果: 15天={formatted['15天']} 30天={formatted['30天']}")


if __name__ == "__main__":
    print("🚀 开始确认操作退货率测试")
    print("=" * 80)
    
    # 测试确认流程
    result = test_confirm_flow()
    
    # 测试边缘情况
    test_edge_cases()
    
    # 测试字段名兼容性
    test_field_names()
    
    print("\n✅ 测试完成！")
