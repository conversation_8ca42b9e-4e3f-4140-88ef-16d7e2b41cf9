=== 开始测试 ===

=== 商品卡退货率计算 ===
颜色: 黑上衣
self.all_skus 数量: 6
self.skus 数量: 2
使用的SKU数量: 6
使用的是: all_skus
计算结果: 48%
==============================

=== set_all_skus 调用 ===
颜色: 黑上衣
设置的all_skus数量: 6
==============================

=== 商品卡退货率计算 ===
颜色: 黑上衣
self.all_skus 数量: 6
self.skus 数量: 2
使用的SKU数量: 6
使用的是: all_skus
计算结果: 48%
==============================

=== set_all_skus 调用 ===
颜色: 黑上衣
设置的all_skus数量: 6
==============================

=== 商品卡退货率计算 ===
颜色: 波点裙
self.all_skus 数量: 6
self.skus 数量: 2
使用的SKU数量: 6
使用的是: all_skus
计算结果: 48%
==============================

=== set_all_skus 调用 ===
颜色: 波点裙
设置的all_skus数量: 6
==============================

=== 商品卡退货率计算 ===
颜色: 一整套
self.all_skus 数量: 6
self.skus 数量: 2
使用的SKU数量: 6
使用的是: all_skus
计算结果: 48%
==============================

=== set_all_skus 调用 ===
颜色: 一整套
设置的all_skus数量: 6
==============================

=== 商品卡退货率计算 ===
颜色: 蓝色上衣
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 蓝色上衣
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 黑上衣
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 黑上衣
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 杏色半裙
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 杏色半裙
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 波点裙
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 波点裙
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 一整套
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 一整套
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 蓝色上衣
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 蓝色上衣
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 黑上衣
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 黑上衣
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 杏色半裙
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 杏色半裙
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 波点裙
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 波点裙
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 一整套
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 一整套
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 蓝色上衣
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 蓝色上衣
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 黑上衣
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 黑上衣
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 杏色半裙
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 杏色半裙
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 波点裙
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 波点裙
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 一整套
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 一整套
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 粉色半裙
self.all_skus 数量: 12
self.skus 数量: 4
使用的SKU数量: 12
使用的是: all_skus
计算结果: 70%
==============================

=== set_all_skus 调用 ===
颜色: 粉色半裙
设置的all_skus数量: 12
==============================

=== 商品卡退货率计算 ===
颜色: 吊带上衣
self.all_skus 数量: 12
self.skus 数量: 4
使用的SKU数量: 12
使用的是: all_skus
计算结果: 70%
==============================

=== set_all_skus 调用 ===
颜色: 吊带上衣
设置的all_skus数量: 12
==============================

=== 商品卡退货率计算 ===
颜色: 黑色吊带
self.all_skus 数量: 12
self.skus 数量: 4
使用的SKU数量: 12
使用的是: all_skus
计算结果: 70%
==============================

=== set_all_skus 调用 ===
颜色: 黑色吊带
设置的all_skus数量: 12
==============================

=== 商品卡退货率计算 ===
颜色: 粉色半裙
self.all_skus 数量: 12
self.skus 数量: 4
使用的SKU数量: 12
使用的是: all_skus
计算结果: 70%
==============================

=== set_all_skus 调用 ===
颜色: 粉色半裙
设置的all_skus数量: 12
==============================

=== 商品卡退货率计算 ===
颜色: 吊带上衣
self.all_skus 数量: 12
self.skus 数量: 4
使用的SKU数量: 12
使用的是: all_skus
计算结果: 70%
==============================

=== set_all_skus 调用 ===
颜色: 吊带上衣
设置的all_skus数量: 12
==============================

=== 商品卡退货率计算 ===
颜色: 黑色吊带
self.all_skus 数量: 12
self.skus 数量: 4
使用的SKU数量: 12
使用的是: all_skus
计算结果: 70%
==============================

=== set_all_skus 调用 ===
颜色: 黑色吊带
设置的all_skus数量: 12
==============================

=== 商品卡退货率计算 ===
颜色: 粉色半裙
self.all_skus 数量: 12
self.skus 数量: 4
使用的SKU数量: 12
使用的是: all_skus
计算结果: 70%
==============================

=== set_all_skus 调用 ===
颜色: 粉色半裙
设置的all_skus数量: 12
==============================

=== 商品卡退货率计算 ===
颜色: 吊带上衣
self.all_skus 数量: 12
self.skus 数量: 4
使用的SKU数量: 12
使用的是: all_skus
计算结果: 70%
==============================

=== set_all_skus 调用 ===
颜色: 吊带上衣
设置的all_skus数量: 12
==============================

=== 商品卡退货率计算 ===
颜色: 黑色吊带
self.all_skus 数量: 12
self.skus 数量: 4
使用的SKU数量: 12
使用的是: all_skus
计算结果: 70%
==============================

=== set_all_skus 调用 ===
颜色: 黑色吊带
设置的all_skus数量: 12
==============================

=== 商品卡退货率计算 ===
颜色: 上衣
self.all_skus 数量: 17
self.skus 数量: 3
使用的SKU数量: 17
使用的是: all_skus
计算结果: -
==============================

=== set_all_skus 调用 ===
颜色: 上衣
设置的all_skus数量: 17
==============================

=== 商品卡退货率计算 ===
颜色: 半裙
self.all_skus 数量: 17
self.skus 数量: 3
使用的SKU数量: 17
使用的是: all_skus
计算结果: -
==============================

=== set_all_skus 调用 ===
颜色: 半裙
设置的all_skus数量: 17
==============================

=== 商品卡退货率计算 ===
颜色: 米白色
self.all_skus 数量: 17
self.skus 数量: 4
使用的SKU数量: 17
使用的是: all_skus
计算结果: -
==============================

=== set_all_skus 调用 ===
颜色: 米白色
设置的all_skus数量: 17
==============================

=== 商品卡退货率计算 ===
颜色: 杏色
self.all_skus 数量: 17
self.skus 数量: 4
使用的SKU数量: 17
使用的是: all_skus
计算结果: -
==============================

=== set_all_skus 调用 ===
颜色: 杏色
设置的all_skus数量: 17
==============================

=== 商品卡退货率计算 ===
颜色: 上衣 半裙
self.all_skus 数量: 17
self.skus 数量: 3
使用的SKU数量: 17
使用的是: all_skus
计算结果: -
==============================

=== set_all_skus 调用 ===
颜色: 上衣 半裙
设置的all_skus数量: 17
==============================

=== 商品卡退货率计算 ===
颜色: 长裙两件套
self.all_skus 数量: 3
self.skus 数量: 3
使用的SKU数量: 3
使用的是: all_skus
计算结果: 53%
==============================

=== set_all_skus 调用 ===
颜色: 长裙两件套
设置的all_skus数量: 3
==============================

=== 商品卡退货率计算 ===
颜色: 杏色
self.all_skus 数量: 4
self.skus 数量: 4
使用的SKU数量: 4
使用的是: all_skus
计算结果: 69%
==============================

=== set_all_skus 调用 ===
颜色: 杏色
设置的all_skus数量: 4
==============================

=== 商品卡退货率计算 ===
颜色: 蓝色上衣
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 蓝色上衣
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 黑上衣
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 黑上衣
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 杏色半裙
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 杏色半裙
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 波点裙
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 波点裙
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 一整套
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 一整套
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 蓝色上衣
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 蓝色上衣
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 黑上衣
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 黑上衣
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 杏色半裙
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 杏色半裙
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 波点裙
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 波点裙
设置的all_skus数量: 20
==============================

=== 商品卡退货率计算 ===
颜色: 一整套
self.all_skus 数量: 20
self.skus 数量: 4
使用的SKU数量: 20
使用的是: all_skus
计算结果: 49%
==============================

=== set_all_skus 调用 ===
颜色: 一整套
设置的all_skus数量: 20
==============================
