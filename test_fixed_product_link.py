#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的商品链接识别和退货率计算逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.product_link_identifier import ProductLinkIdentifier
from modules.return_rate_calculator import ReturnRateCalculator


def test_exact_matching():
    """测试精确匹配的商品链接识别"""
    print("🧪 测试精确匹配的商品链接识别")
    print("=" * 60)
    
    # 测试数据：完全相同的商品名称和创建时间应该被识别为同一个商品链接
    test_skus = [
        {
            "sku_id": "GD653-6258-薄荷绿-S",
            "name": "时尚女装连衣裙 春季新款",  # 完全相同
            "created": "2024-01-15 10:00:00",  # 完全相同
            "sent_qty_15": 20,
            "as_qty_15": 8,
            "sent_qty_30": 25,
            "as_qty_30": 10,
        },
        {
            "sku_id": "GD653-6258-薄荷绿-M",
            "name": "时尚女装连衣裙 春季新款",  # 完全相同
            "created": "2024-01-15 10:00:00",  # 完全相同
            "sent_qty_15": 18,
            "as_qty_15": 7,
            "sent_qty_30": 22,
            "as_qty_30": 9,
        },
        {
            "sku_id": "GD653-6258-黑色-S",
            "name": "时尚女装连衣裙 春季新款 优质面料",  # 不同的商品名称
            "created": "2024-01-25 14:00:00",  # 不同的创建时间
            "sent_qty_15": 15,
            "as_qty_15": 6,
            "sent_qty_30": 18,
            "as_qty_30": 7,
        },
        {
            "sku_id": "GD653-6258-黑色-M",
            "name": "时尚女装连衣裙 春季新款 优质面料",  # 与黑色-S相同
            "created": "2024-01-25 14:00:00",  # 与黑色-S相同
            "sent_qty_15": 12,
            "as_qty_15": 5,
            "sent_qty_30": 16,
            "as_qty_30": 6,
        }
    ]
    
    print(f"📊 测试数据：{len(test_skus)} 个SKU")
    for sku in test_skus:
        print(f"  - {sku['sku_id']}: {sku['name']} ({sku['created']})")
    
    # 测试商品链接识别
    print("\n🔗 商品链接识别测试（精确匹配）:")
    identifier = ProductLinkIdentifier()
    product_links = identifier.identify_product_links(test_skus)
    
    print(f"✅ 识别出 {len(product_links)} 个商品链接:")
    for link_id, link_skus in product_links.items():
        print(f"\n📦 {link_id} ({len(link_skus)} 个SKU):")
        first_sku = link_skus[0]
        print(f"  商品名称: {first_sku['name']}")
        print(f"  创建时间: {first_sku['created']}")
        print(f"  包含SKU:")
        for sku in link_skus:
            print(f"    - {sku['sku_id']}")
    
    # 验证结果
    expected_links = 2  # 应该有2个不同的商品链接
    if len(product_links) == expected_links:
        print(f"\n✅ 测试通过：正确识别出 {expected_links} 个商品链接")
    else:
        print(f"\n❌ 测试失败：期望 {expected_links} 个链接，实际 {len(product_links)} 个")
    
    return product_links


def test_return_rate_calculation():
    """测试商品链接级别的退货率计算"""
    print("\n🧪 测试商品链接级别的退货率计算")
    print("=" * 60)
    
    # 模拟商品卡的场景：单个颜色规格 vs 整个商品链接
    single_color_skus = [
        {
            "sku_id": "GD653-6258-薄荷绿-S",
            "name": "时尚女装连衣裙 春季新款",
            "created": "2024-01-15 10:00:00",
            "sent_qty_15": 20,
            "as_qty_15": 8,
            "sent_qty_30": 25,
            "as_qty_30": 10,
        },
        {
            "sku_id": "GD653-6258-薄荷绿-M",
            "name": "时尚女装连衣裙 春季新款",
            "created": "2024-01-15 10:00:00",
            "sent_qty_15": 18,
            "as_qty_15": 7,
            "sent_qty_30": 22,
            "as_qty_30": 9,
        }
    ]
    
    all_link_skus = [
        {
            "sku_id": "GD653-6258-薄荷绿-S",
            "name": "时尚女装连衣裙 春季新款",
            "created": "2024-01-15 10:00:00",
            "sent_qty_15": 20,
            "as_qty_15": 8,
            "sent_qty_30": 25,
            "as_qty_30": 10,
        },
        {
            "sku_id": "GD653-6258-薄荷绿-M",
            "name": "时尚女装连衣裙 春季新款",
            "created": "2024-01-15 10:00:00",
            "sent_qty_15": 18,
            "as_qty_15": 7,
            "sent_qty_30": 22,
            "as_qty_30": 9,
        },
        {
            "sku_id": "GD653-6258-黑色-S",
            "name": "时尚女装连衣裙 春季新款",
            "created": "2024-01-15 10:00:00",
            "sent_qty_15": 15,
            "as_qty_15": 6,
            "sent_qty_30": 18,
            "as_qty_30": 7,
        },
        {
            "sku_id": "GD653-6258-黑色-M",
            "name": "时尚女装连衣裙 春季新款",
            "created": "2024-01-15 10:00:00",
            "sent_qty_15": 12,
            "as_qty_15": 5,
            "sent_qty_30": 16,
            "as_qty_30": 6,
        }
    ]
    
    calculator = ReturnRateCalculator()
    
    # 测试1：单个颜色规格的退货率
    print("🔍 测试1：单个颜色规格（薄荷绿）的退货率")
    single_color_rate = calculator.get_card_return_rate_30_day(single_color_skus)
    print(f"  单颜色退货率: {single_color_rate}")
    
    # 计算详细数据
    total_sent_single = sum(sku['sent_qty_30'] for sku in single_color_skus)
    total_returned_single = sum(sku['as_qty_30'] for sku in single_color_skus)
    expected_single = (total_returned_single / total_sent_single) * 100 if total_sent_single > 0 else 0
    print(f"  预期计算: {total_returned_single}/{total_sent_single} = {expected_single:.1f}%")
    
    # 测试2：整个商品链接的退货率
    print("\n🔍 测试2：整个商品链接（所有颜色）的退货率")
    all_link_rate = calculator.get_card_return_rate_30_day(all_link_skus)
    print(f"  整链接退货率: {all_link_rate}")
    
    # 计算详细数据
    total_sent_all = sum(sku['sent_qty_30'] for sku in all_link_skus)
    total_returned_all = sum(sku['as_qty_30'] for sku in all_link_skus)
    expected_all = (total_returned_all / total_sent_all) * 100 if total_sent_all > 0 else 0
    print(f"  预期计算: {total_returned_all}/{total_sent_all} = {expected_all:.1f}%")
    
    # 比较结果
    print(f"\n📊 结果比较:")
    print(f"  单颜色规格退货率: {single_color_rate}")
    print(f"  整个商品链接退货率: {all_link_rate}")
    
    if single_color_rate != all_link_rate:
        print("✅ 测试通过：单颜色和整链接的退货率不同，符合预期")
        print("💡 修复后应该使用整个商品链接的退货率")
    else:
        print("⚠️ 注意：单颜色和整链接的退货率相同")


def test_edge_cases():
    """测试边缘情况"""
    print("\n🧪 测试边缘情况")
    print("=" * 60)
    
    identifier = ProductLinkIdentifier()
    
    # 测试1：完全相同的SKU（除了规格）
    print("🔍 测试1：完全相同的商品（不同规格）")
    identical_skus = [
        {
            "sku_id": "GD653-6258-红色-S",
            "name": "时尚女装连衣裙",
            "created": "2024-01-15 10:00:00",
        },
        {
            "sku_id": "GD653-6258-红色-M",
            "name": "时尚女装连衣裙",
            "created": "2024-01-15 10:00:00",
        },
        {
            "sku_id": "GD653-6258-红色-L",
            "name": "时尚女装连衣裙",
            "created": "2024-01-15 10:00:00",
        }
    ]
    
    links = identifier.identify_product_links(identical_skus)
    print(f"  结果: {len(links)} 个商品链接（期望: 1个）")
    
    # 测试2：微小差异的商品名称
    print("\n🔍 测试2：微小差异的商品名称")
    similar_skus = [
        {
            "sku_id": "GD653-6258-蓝色-S",
            "name": "时尚女装连衣裙",
            "created": "2024-01-15 10:00:00",
        },
        {
            "sku_id": "GD653-6258-蓝色-M",
            "name": "时尚女装连衣裙 ",  # 末尾多一个空格
            "created": "2024-01-15 10:00:00",
        }
    ]
    
    links = identifier.identify_product_links(similar_skus)
    print(f"  结果: {len(links)} 个商品链接（期望: 2个，因为名称不完全相同）")
    
    # 测试3：时间格式差异
    print("\n🔍 测试3：时间格式差异")
    time_diff_skus = [
        {
            "sku_id": "GD653-6258-绿色-S",
            "name": "时尚女装连衣裙",
            "created": "2024-01-15 10:00:00",
        },
        {
            "sku_id": "GD653-6258-绿色-M",
            "name": "时尚女装连衣裙",
            "created": "2024-01-15 10:00:01",  # 相差1秒
        }
    ]
    
    links = identifier.identify_product_links(time_diff_skus)
    print(f"  结果: {len(links)} 个商品链接（期望: 2个，因为时间不完全相同）")


if __name__ == "__main__":
    print("🚀 开始测试修复后的商品链接逻辑")
    print("=" * 80)
    
    # 测试精确匹配
    product_links = test_exact_matching()
    
    # 测试退货率计算
    test_return_rate_calculation()
    
    # 测试边缘情况
    test_edge_cases()
    
    print("\n✅ 测试完成！")
    print("\n📋 修复总结:")
    print("1. ✅ 商品链接识别改为精确匹配（商品名称和创建时间100%一致）")
    print("2. ✅ 商品卡退货率计算改为商品链接级别（而不是单个颜色规格级别）")
    print("3. ✅ 不同商品链接分开计算，相同商品链接合并计算")
