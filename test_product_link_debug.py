#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品链接识别调试测试
测试实际ERP数据的商品链接识别效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.product_link_identifier import ProductLinkIdentifier
from modules.return_rate_calculator import ReturnRateCalculator


def test_real_sku_data():
    """测试真实的SKU数据结构"""
    print("🧪 测试真实SKU数据的商品链接识别")
    print("=" * 60)
    
    # 模拟从ERP API返回的真实数据结构（基于erp_integration.py中的字段）
    test_skus = [
        {
            "sku_id": "GD653-6258-薄荷绿-S",
            "name": "时尚女装连衣裙 春季新款",  # ERP API使用name字段
            "item_name": "时尚女装连衣裙 春季新款",
            "created": "2024-01-15 10:00:00",  # ERP API使用created字段
            "modified": "2024-01-15 10:05:00",
            "sent_qty_15": 20,
            "as_qty_15": 8,
            "sent_qty_30": 25,
            "as_qty_30": 10,
            "cost_price": 45.0,
            "sale_price": 89.0
        },
        {
            "sku_id": "GD653-6258-薄荷绿-M",
            "name": "时尚女装连衣裙 春季新款",
            "item_name": "时尚女装连衣裙 春季新款",
            "created": "2024-01-15 10:05:00",
            "modified": "2024-01-15 10:10:00",
            "sent_qty_15": 18,
            "as_qty_15": 7,
            "sent_qty_30": 22,
            "as_qty_30": 9,
            "cost_price": 45.0,
            "sale_price": 89.0
        },
        {
            "sku_id": "GD653-6258-黑色-S",
            "name": "时尚女装连衣裙 春季新款 优质面料",  # 稍微不同的商品名称
            "item_name": "时尚女装连衣裙 春季新款 优质面料",
            "created": "2024-01-25 14:00:00",  # 不同的创建时间
            "modified": "2024-01-25 14:05:00",
            "sent_qty_15": 15,
            "as_qty_15": 6,
            "sent_qty_30": 18,
            "as_qty_30": 7,
            "cost_price": 48.0,
            "sale_price": 95.0
        },
        {
            "sku_id": "GD653-6258-黑色-M",
            "name": "时尚女装连衣裙 春季新款 优质面料",
            "item_name": "时尚女装连衣裙 春季新款 优质面料",
            "created": "2024-01-25 14:10:00",
            "modified": "2024-01-25 14:15:00",
            "sent_qty_15": 12,
            "as_qty_15": 5,
            "sent_qty_30": 16,
            "as_qty_30": 6,
            "cost_price": 48.0,
            "sale_price": 95.0
        }
    ]
    
    print(f"📊 测试数据：{len(test_skus)} 个SKU")
    for sku in test_skus:
        print(f"  - {sku['sku_id']}: {sku['name']} ({sku['created']})")
    
    # 测试商品链接识别
    print("\n🔗 商品链接识别测试:")
    identifier = ProductLinkIdentifier()
    product_links = identifier.identify_product_links(test_skus)
    
    print(f"✅ 识别出 {len(product_links)} 个商品链接:")
    for link_id, link_skus in product_links.items():
        print(f"\n📦 {link_id} ({len(link_skus)} 个SKU):")
        for sku in link_skus:
            print(f"  - {sku['sku_id']}: {sku['name']}")
            print(f"    创建时间: {sku['created']}")
            print(f"    15天数据: 发货{sku['sent_qty_15']}件, 退货{sku['as_qty_15']}件")
            print(f"    30天数据: 发货{sku['sent_qty_30']}件, 退货{sku['as_qty_30']}件")
    
    # 测试退货率计算
    print("\n📊 退货率计算测试:")
    calculator = ReturnRateCalculator()
    link_rates = calculator.calculate_return_rates_by_product_links(test_skus)
    
    print(f"✅ 计算出 {len(link_rates)} 个链接的退货率:")
    for link_id, rates in link_rates.items():
        print(f"  {link_id}: 15天={rates['15天']:.1f}%, 30天={rates['30天']:.1f}%")
    
    # 测试格式化显示
    formatted_rates = calculator.format_multi_link_display(link_rates)
    print(f"\n📋 格式化显示结果:")
    print(f"  15天退货率: {formatted_rates['15天']}")
    print(f"  30天退货率: {formatted_rates['30天']}")
    
    return product_links, link_rates


def test_similarity_threshold():
    """测试不同相似度阈值的效果"""
    print("\n🎯 测试相似度阈值效果")
    print("=" * 60)
    
    test_skus = [
        {
            "sku_id": "GD653-6258-薄荷绿-S",
            "name": "时尚女装连衣裙 春季新款",
            "created": "2024-01-15 10:00:00",
        },
        {
            "sku_id": "GD653-6258-黑色-S",
            "name": "时尚女装连衣裙 春季新款 优质面料",  # 相似度约0.85
            "created": "2024-01-25 14:00:00",
        },
        {
            "sku_id": "GD653-6258-白色-S",
            "name": "时尚女装连衣裙 夏季新款",  # 相似度约0.75
            "created": "2024-02-15 10:00:00",
        }
    ]
    
    thresholds = [0.7, 0.8, 0.85, 0.9]
    
    for threshold in thresholds:
        print(f"\n🔍 相似度阈值: {threshold}")
        identifier = ProductLinkIdentifier(title_similarity_threshold=threshold)
        product_links = identifier.identify_product_links(test_skus)
        print(f"  识别出 {len(product_links)} 个商品链接")
        
        for link_id, link_skus in product_links.items():
            sku_names = [sku['name'] for sku in link_skus]
            print(f"    {link_id}: {len(link_skus)} 个SKU")
            for name in sku_names:
                print(f"      - {name}")


def test_time_window():
    """测试时间窗口的效果"""
    print("\n⏰ 测试时间窗口效果")
    print("=" * 60)
    
    test_skus = [
        {
            "sku_id": "GD653-6258-薄荷绿-S",
            "name": "时尚女装连衣裙 春季新款",
            "created": "2024-01-15 10:00:00",
        },
        {
            "sku_id": "GD653-6258-薄荷绿-M",
            "name": "时尚女装连衣裙 春季新款",
            "created": "2024-01-20 10:00:00",  # 5天后
        },
        {
            "sku_id": "GD653-6258-薄荷绿-L",
            "name": "时尚女装连衣裙 春季新款",
            "created": "2024-01-30 10:00:00",  # 15天后
        }
    ]
    
    time_windows = [3, 7, 14, 30]
    
    for window in time_windows:
        print(f"\n📅 时间窗口: {window} 天")
        identifier = ProductLinkIdentifier(time_window_days=window)
        product_links = identifier.identify_product_links(test_skus)
        print(f"  识别出 {len(product_links)} 个商品链接")
        
        for link_id, link_skus in product_links.items():
            dates = [sku['created'] for sku in link_skus]
            print(f"    {link_id}: {len(link_skus)} 个SKU")
            for date in dates:
                print(f"      - {date}")


if __name__ == "__main__":
    print("🚀 开始商品链接识别调试测试")
    print("=" * 80)
    
    # 测试真实数据
    product_links, link_rates = test_real_sku_data()
    
    # 测试相似度阈值
    test_similarity_threshold()
    
    # 测试时间窗口
    test_time_window()
    
    print("\n✅ 测试完成！")
