#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的退货率计算器
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.return_rate_calculator import ReturnRateCalculator


def test_enhanced_return_rate_calculator():
    """测试增强的退货率计算器"""
    print("🧪 测试增强的退货率计算器")
    print("=" * 50)
    
    calculator = ReturnRateCalculator()
    
    # 测试数据：模拟GD340-9402的情况，2个商品链接
    test_skus = [
        # 商品链接1：时尚女装连衣裙 春季新款
        {
            "sku_id": "GD340-9402-红色-S",
            "product_title": "时尚女装连衣裙 春季新款",
            "create_time": "2024-01-15 10:00:00",
            "sent_qty_15": 10,
            "as_qty_15": 2,
            "sent_qty_30": 15,
            "as_qty_30": 3
        },
        {
            "sku_id": "GD340-9402-红色-M", 
            "product_title": "时尚女装连衣裙 春季新款",
            "create_time": "2024-01-15 10:05:00",
            "sent_qty_15": 8,
            "as_qty_15": 1,
            "sent_qty_30": 12,
            "as_qty_30": 2
        },
        # 商品链接2：时尚女装连衣裙 春季新款 优质面料
        {
            "sku_id": "GD340-9402-蓝色-S",
            "product_title": "时尚女装连衣裙 春季新款 优质面料",
            "create_time": "2024-01-25 14:00:00",
            "sent_qty_15": 20,
            "as_qty_15": 8,
            "sent_qty_30": 25,
            "as_qty_30": 10
        },
        {
            "sku_id": "GD340-9402-蓝色-M",
            "product_title": "时尚女装连衣裙 春季新款 优质面料", 
            "create_time": "2024-01-25 14:10:00",
            "sent_qty_15": 15,
            "as_qty_15": 6,
            "sent_qty_30": 18,
            "as_qty_30": 7
        }
    ]
    
    print("📊 测试数据:")
    for sku in test_skus:
        print(f"  - {sku['sku_id']}: {sku['product_title']} ({sku['create_time']})")
        print(f"    15天: 发货{sku['sent_qty_15']}件, 退货{sku['as_qty_15']}件")
        print(f"    30天: 发货{sku['sent_qty_30']}件, 退货{sku['as_qty_30']}件")
    
    # 测试商品链接级别计算
    print("\n🔗 商品链接级别退货率计算:")
    link_rates = calculator.calculate_return_rates_by_product_links(test_skus)
    for link_id, rates in link_rates.items():
        print(f"  {link_id}: 15天={rates['15天']:.1f}%, 30天={rates['30天']:.1f}%")
    
    # 测试多链接显示格式
    print("\n📋 多链接显示格式:")
    formatted_display = calculator.format_multi_link_display(link_rates)
    print(f"  15退: {formatted_display['15天']}")
    print(f"  30退: {formatted_display['30天']}")
    
    # 测试商品卡30天退货率
    print("\n🏷️ 商品卡30天退货率:")
    card_rate = calculator.get_card_return_rate_30_day(test_skus)
    print(f"  显示: {card_rate}")
    
    # 测试兼容性：旧接口
    print("\n🔄 兼容性测试（旧接口）:")
    old_rates = calculator.calculate_return_rates_by_sku_code(test_skus)
    print(f"  旧接口结果: {old_rates}")
    
    old_formatted = calculator.format_return_rate_integer(old_rates)
    print(f"  旧格式化结果: {old_formatted}")
    
    print("\n✅ 测试完成！")
    return True


def test_single_link_case():
    """测试单商品链接情况"""
    print("\n🧪 测试单商品链接情况")
    print("=" * 50)
    
    calculator = ReturnRateCalculator()
    
    # 测试数据：只有一个商品链接
    single_link_skus = [
        {
            "sku_id": "GT217-7528-红色-S",
            "product_title": "经典款T恤",
            "create_time": "2024-02-01 09:00:00",
            "sent_qty_15": 30,
            "as_qty_15": 3,
            "sent_qty_30": 40,
            "as_qty_30": 4
        },
        {
            "sku_id": "GT217-7528-红色-M",
            "product_title": "经典款T恤",
            "create_time": "2024-02-01 09:10:00",
            "sent_qty_15": 25,
            "as_qty_15": 2,
            "sent_qty_30": 35,
            "as_qty_30": 3
        }
    ]
    
    print("📊 单链接测试数据:")
    for sku in single_link_skus:
        print(f"  - {sku['sku_id']}: {sku['product_title']}")
    
    # 测试商品链接级别计算
    link_rates = calculator.calculate_return_rates_by_product_links(single_link_skus)
    print(f"\n🔗 识别出 {len(link_rates)} 个商品链接")
    
    # 测试显示格式
    formatted_display = calculator.format_multi_link_display(link_rates)
    print(f"\n📋 单链接显示格式:")
    print(f"  15退: {formatted_display['15天']}")
    print(f"  30退: {formatted_display['30天']}")
    
    # 测试商品卡显示
    card_rate = calculator.get_card_return_rate_30_day(single_link_skus)
    print(f"\n🏷️ 商品卡显示: {card_rate}")
    
    print("\n✅ 单链接测试完成！")


if __name__ == "__main__":
    test_enhanced_return_rate_calculator()
    test_single_link_case()
