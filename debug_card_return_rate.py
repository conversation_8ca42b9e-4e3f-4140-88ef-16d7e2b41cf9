#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试商品卡退货率计算问题
模拟实际的商品卡创建和退货率计算流程
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QWidget
from modules.color_confirm_panel import ColorSpecCard
from modules.return_rate_calculator import ReturnRateCalculator


def test_card_return_rate_calculation():
    """测试商品卡退货率计算流程"""
    print("🧪 测试商品卡退货率计算流程")
    print("=" * 60)
    
    # 模拟GD340-9376的数据（从截图看到的款号）
    color_groups = {
        "半身裙": [
            {
                "sku_id": "GD340-9376-半身裙-S",
                "name": "时尚女装套装 春季新款",
                "created": "2024-01-15 10:00:00",
                "sent_qty_15": 20,
                "as_qty_15": 8,
                "sent_qty_30": 25,
                "as_qty_30": 10,
                "cost_price": 45.0,
                "sale_price": 89.0
            },
            {
                "sku_id": "GD340-9376-半身裙-M",
                "name": "时尚女装套装 春季新款",
                "created": "2024-01-15 10:00:00",
                "sent_qty_15": 18,
                "as_qty_15": 7,
                "sent_qty_30": 22,
                "as_qty_30": 9,
                "cost_price": 45.0,
                "sale_price": 89.0
            }
        ],
        "毛衣": [
            {
                "sku_id": "GD340-9376-毛衣-S",
                "name": "时尚女装套装 春季新款",
                "created": "2024-01-15 10:00:00",
                "sent_qty_15": 15,
                "as_qty_15": 6,
                "sent_qty_30": 18,
                "as_qty_30": 7,
                "cost_price": 48.0,
                "sale_price": 95.0
            },
            {
                "sku_id": "GD340-9376-毛衣-M",
                "name": "时尚女装套装 春季新款",
                "created": "2024-01-15 10:00:00",
                "sent_qty_15": 12,
                "as_qty_15": 5,
                "sent_qty_30": 16,
                "as_qty_30": 6,
                "cost_price": 48.0,
                "sale_price": 95.0
            }
        ],
        "两件套装": [
            {
                "sku_id": "GD340-9376-两件套装-S",
                "name": "时尚女装套装 春季新款",
                "created": "2024-01-15 10:00:00",
                "sent_qty_15": 10,
                "as_qty_15": 8,
                "sent_qty_30": 12,
                "as_qty_30": 10,
                "cost_price": 65.0,
                "sale_price": 129.0
            },
            {
                "sku_id": "GD340-9376-两件套装-M",
                "name": "时尚女装套装 春季新款",
                "created": "2024-01-15 10:00:00",
                "sent_qty_15": 8,
                "as_qty_15": 6,
                "sent_qty_30": 10,
                "as_qty_30": 8,
                "cost_price": 65.0,
                "sale_price": 129.0
            }
        ]
    }
    
    # 收集所有SKU
    all_skus = []
    for color_name, skus in color_groups.items():
        all_skus.extend(skus)
    
    print(f"📊 测试数据：")
    print(f"  颜色组数量: {len(color_groups)}")
    print(f"  总SKU数量: {len(all_skus)}")
    
    # 计算各种退货率
    calculator = ReturnRateCalculator()
    
    print(f"\n🔍 测试1：按颜色规格级别计算（修复前的逻辑）")
    for color_name, skus in color_groups.items():
        rate = calculator.get_card_return_rate_30_day(skus)
        
        # 手动计算验证
        total_sent = sum(sku['sent_qty_30'] for sku in skus)
        total_returned = sum(sku['as_qty_30'] for sku in skus)
        expected = (total_returned / total_sent) * 100 if total_sent > 0 else 0
        
        print(f"  {color_name}: {rate} (预期: {expected:.1f}%)")
    
    print(f"\n🔍 测试2：按商品链接级别计算（修复后的逻辑）")
    link_rate = calculator.get_card_return_rate_30_day(all_skus)
    
    # 手动计算验证
    total_sent_all = sum(sku['sent_qty_30'] for sku in all_skus)
    total_returned_all = sum(sku['as_qty_30'] for sku in all_skus)
    expected_all = (total_returned_all / total_sent_all) * 100 if total_sent_all > 0 else 0
    
    print(f"  整个商品链接: {link_rate} (预期: {expected_all:.1f}%)")
    
    print(f"\n📋 结论：")
    if link_rate != "55%" and link_rate != "50%" and link_rate != "80%":
        print(f"✅ 修复生效：所有颜色应该显示相同的退货率 {link_rate}")
    else:
        print(f"❌ 修复未生效：仍在按颜色规格计算")
    
    return color_groups, all_skus


def test_card_creation_simulation():
    """模拟商品卡创建过程"""
    print("\n🧪 模拟商品卡创建过程")
    print("=" * 60)
    
    # 创建QApplication（GUI组件需要）
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # 获取测试数据
    color_groups, all_skus = test_card_return_rate_calculation()
    
    print(f"\n🔍 模拟创建商品卡：")
    
    # 模拟创建每个颜色的卡片
    cards = []
    for color_name, skus in color_groups.items():
        print(f"\n📦 创建 {color_name} 卡片：")
        
        # 创建卡片（模拟ColorSpecCard的创建）
        try:
            # 注意：这里可能会因为GUI组件而失败，但我们主要关注退货率计算
            card = ColorSpecCard(color_name, skus, 89.0, "GD340-9376")
            
            # 设置所有SKU数据（这是关键步骤）
            print(f"  设置all_skus数据...")
            card.set_all_skus(all_skus)
            
            # 获取退货率显示
            rate_display = card.get_return_rate_display()
            print(f"  退货率显示: {rate_display}")
            
            cards.append(card)
            
        except Exception as e:
            print(f"  ❌ 创建卡片失败: {str(e)}")
            # 即使GUI创建失败，我们也可以测试退货率计算逻辑
            print(f"  📊 直接测试退货率计算:")
            calculator = ReturnRateCalculator()
            
            # 测试单颜色计算
            single_rate = calculator.get_card_return_rate_30_day(skus)
            print(f"    单颜色退货率: {single_rate}")
            
            # 测试整个链接计算
            link_rate = calculator.get_card_return_rate_30_day(all_skus)
            print(f"    整链接退货率: {link_rate}")
    
    return cards


def main():
    """主函数"""
    print("🚀 开始调试商品卡退货率计算问题")
    print("=" * 80)
    
    # 测试1：退货率计算逻辑
    test_card_return_rate_calculation()
    
    # 测试2：模拟卡片创建
    test_card_creation_simulation()
    
    print("\n✅ 调试测试完成！")
    print("\n📋 如果看到以上调试输出，说明代码修改已生效")
    print("📋 如果界面仍显示不同退货率，可能是缓存或UI刷新问题")


if __name__ == "__main__":
    main()
