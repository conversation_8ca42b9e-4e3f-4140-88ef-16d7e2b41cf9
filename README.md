# 智能票据处理系统

一个基于AI和PyQt5开发的智能票据处理系统，能够自动识别和解析票据图像，提取商品信息，并与ERP系统集成进行成本价管理和利润分析。系统采用模块化架构设计，具备完整的状态管理机制和现代化用户界面。

> **📋 完整技术文档请查看：[项目技术文档.md](./项目技术文档.md)**

## 🌟 核心功能

### 🤖 AI智能解析
- **多模型支持**：支持doubao-1.5-vision-pro等主流视觉AI模型
- **智能识别**：自动识别票据中的商品信息（款号、颜色规格、数量、单价等）
- **格式兼容**：支持多种票据格式和图像类型（JPG、PNG、BMP等）
- **图像分割**：智能图像分割功能，处理长票据和复杂布局
- **数据标准化**：结构化数据提取和格式标准化处理

### 🔍 ERP系统集成
- **实时查询**：实时查询ERP商品信息和价格数据
- **智能匹配**：基于编辑距离的智能SKU匹配算法
- **批量操作**：支持批量成本价更新和库存查询
- **状态管理**：完整的查询状态跟踪和结果缓存
- **认证管理**：自动Cookie管理和认证状态检查

### 💰 成本价管理
- **利润计算**：自动计算商品利润和毛利率
- **颜色确认**：可视化颜色规格确认面板
- **价格对比**：实时显示ERP售价与票据成本价对比
- **状态显示**：价格上涨红色、价格下降绿色的直观显示
- **批量更新**：支持选择性批量更新ERP系统成本价

### 🎨 现代化界面
- **深色主题**：基于PyQt5的美观界面设计，护眼黑色主题
- **模块化布局**：标签页式布局，功能区域清晰分离
- **图像管理**：完整的图像预览、缩略图和全屏显示功能
- **进度反馈**：统一的进度条显示，实时操作状态反馈
- **响应式设计**：自适应布局和流畅的交互体验

### 🔒 数据状态管理
- **状态隔离**：完整的表格数据状态隔离机制
- **持久化**：多文件处理时的状态持久化和恢复
- **精确匹配**：基于商品唯一标识的精确匹配算法
- **污染防护**：避免数据污染的核心技术方案

## 🚀 快速开始

### 系统要求
- **操作系统**: Windows 10/11
- **Python版本**: 3.8 或更高版本
- **内存**: 至少 4GB RAM
- **网络**: 稳定的互联网连接（AI服务和ERP查询）

### 安装步骤

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd 自动填写成本价
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **启动程序**
   - 方式一：双击运行 `启动程序.bat`
   - 方式二：命令行运行 `python main.py`

### 首次配置

1. **AI服务配置**
   - 在"⚙️ 配置设置"页面配置AI API密钥和接口地址
   - 选择合适的AI模型（推荐：doubao-1.5-vision-pro）

2. **ERP访问配置**
   - 配置ERP系统访问权限
   - 更新Cookie信息（位于 `latest_cookies.json`）

## 📁 项目架构

### 📂 根目录结构
```
自动填写成本价/
├── 📄 main.py                          # 程序启动入口
├── 🚀 启动程序.bat                     # Windows快速启动脚本
├── 📋 requirements.txt                 # Python依赖包列表
├── 📄 README.md                        # 项目说明文档（本文件）
├── 📄 项目技术文档.md                  # 完整技术文档和架构说明
├── 📄 ERP_API_文档.md                  # ERP API接口详细文档
├── 📄 自定义图像分割功能说明.md        # 图像分割功能使用说明
│
├── 📂 modules/                         # 核心功能模块目录
├── 📂 beifen/                          # 备份文件存储目录
├── 📂 temp_segments/                   # 临时图像片段存储（自动清理）
│
├── 📄 user_config.json                 # 用户个人配置文件
├── 📄 suppliers.json                   # 供应商数据库
├── 📄 file_manager.json                # 文件管理配置
└── 📄 latest_cookies.json              # ERP系统访问Cookie
```

### 📂 modules/ 核心模块详解

#### 🎯 主要功能模块
```
modules/
├── 📄 __init__.py                      # 模块包初始化
├── 🎨 pyqt5_main_gui.py               # 主界面入口（重构后轻量版）
├── 🤖 ai_processor.py                  # AI解析处理核心
├── 🔍 erp_integration.py               # ERP系统集成接口
├── 🎯 sku_matcher.py                   # 智能SKU匹配算法
├── ⚙️ config_manager.py                # 配置管理器
├── 📁 file_manager.py                  # 文件管理器
├── 🖼️ image_splitter.py                # 图像分割处理
├── 🏢 supplier_manager.py              # 供应商管理
├── 🎨 color_confirm_panel.py           # 颜色确认面板
├── 📊 table_manager.py                 # 表格数据管理
├── 🎨 ui_style_manager.py              # UI样式管理器
├── 🎨 ui_constants.py                  # UI常量定义
└── 📂 cookies/                         # Cookie管理相关文件
```

#### 🖥️ GUI界面模块 (modules/gui/)
```
gui/
├── 📄 __init__.py                      # GUI模块初始化
├── 🏠 main_gui_controller.py           # 主界面控制器（核心窗口管理）
├── 🖼️ image_manager.py                 # 图像管理器（预览、缩略图、全屏）
├── 📊 table_ui_manager.py              # 表格界面管理（数据表格、状态显示）
├── 🤖 ai_processor_ui.py               # AI处理界面（解析控制、进度显示）
├── ⚙️ config_ui_manager.py             # 配置界面管理（所有配置页面）
├── 🔍 erp_ui_manager.py                # ERP界面管理（查询、更新界面）
├── 🎮 event_handlers.py                # 事件处理器（用户交互事件）
├── 🎴 image_card_widget.py             # 图像卡片组件（缩略图显示）
├── 🖼️ image_split_overlay.py           # 图像分割覆盖层（分割线管理）
└── 🍪 cookies_manager_widget.py        # Cookie管理组件（ERP认证）
```

#### 🧵 线程处理模块 (modules/threads/)
```
threads/
├── 📄 __init__.py                      # 线程模块初始化
├── 🤖 ai_processing_thread.py          # AI处理线程（图像解析）
├── 🔍 erp_query_thread.py              # ERP查询线程（商品信息查询）
├── 💰 cost_update_thread.py            # 成本价更新线程（批量更新）
└── 📝 thread_safe_logger.py            # 线程安全日志器（多线程日志）
```

#### 🛠️ 工具模块 (modules/utils/)
```
utils/
├── 📄 __init__.py                      # 工具模块初始化
└── 📝 logger.py                        # 日志管理工具（统一日志接口）
```

## 🔧 配置说明

### AI服务配置 (user_config.json)
```json
{
  "ai": {
    "api_key": "your-api-key-here",
    "api_url": "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
    "model_name": "doubao-1.5-vision-pro",
    "temperature": 70,
    "max_tokens": 4000,
    "timeout": 60
  }
}
```

### ERP访问配置 (latest_cookies.json)
```json
{
  "cookies": {
    "session_id": "your-session-id",
    "auth_token": "your-auth-token"
  }
}
```

### 供应商管理 (suppliers.json)
```json
{
  "suppliers": [
    {
      "name": "供应商名称",
      "code": "供应商编码",
      "contact": "联系方式"
    }
  ]
}
```

## 🎯 使用流程

### 第一步：图像处理
1. **选择图像**：点击"📁 上传图像"或使用"📋 粘贴图像"（Ctrl+V）
2. **图像预览**：在左侧面板查看图像预览和缩略图
3. **图像分割**：对于长票据，使用智能分割功能（详见[自定义图像分割功能说明.md](./自定义图像分割功能说明.md)）

### 第二步：AI解析
1. **启动解析**：点击"🤖 AI解析票据"按钮
2. **进度监控**：底部状态栏显示解析进度（紫色进度条）
3. **查看结果**：在右侧表格查看解析出的商品信息
4. **数据验证**：检查款号、颜色规格、数量、单价等信息

### 第三步：ERP查询
1. **商品查询**：点击"🔍 查询ERP"获取商品信息
2. **进度监控**：底部状态栏显示查询进度
3. **SKU匹配**：系统自动匹配票据商品与ERP中的SKU
4. **状态显示**：查看查询状态（🟡待确认、✅已查询等）

### 第四步：颜色确认
1. **颜色选择**：在颜色确认面板选择正确的颜色规格
2. **价格对比**：查看ERP售价与票据成本价的对比
3. **利润计算**：实时显示利润和毛利率
4. **状态更新**：确认后状态显示价格变化（📈上涨红色、📉下降绿色）

### 第五步：成本价更新
1. **选择更新**：选择需要更新的商品（支持批量选择）
2. **执行更新**：点击"💰 上传成本"同步到ERP系统
3. **进度监控**：底部状态栏显示上传进度
4. **结果反馈**：查看更新结果和状态显示

## 🎨 界面特色

### 🌙 深色主题设计
- **护眼设计**：黑色主题界面，长时间使用不疲劳
- **现代风格**：扁平化设计风格，圆角元素，视觉舒适
- **清晰层次**：明确的视觉层次和布局，功能区域分明
- **紫色主题**：统一的紫色进度条和按钮主题色

### 🖼️ 图像管理功能
- **格式支持**：支持JPG、PNG、BMP等多种图像格式
- **智能预览**：自动生成缩略图和预览，支持全屏查看
- **分割功能**：智能图像分割，支持自定义分割线
- **拖拽上传**：支持拖拽上传和Ctrl+V粘贴图像
- **片段管理**：自动管理临时图像片段，程序关闭时自动清理

### 📊 数据可视化
- **状态颜色**：价格上涨红色、下降绿色、待确认橙色
- **利润显示**：实时利润计算，低于20的利润红色显示
- **进度反馈**：统一的底部进度条，实时显示操作进度
- **表格管理**：清晰的表格数据展示，支持排序和筛选
- **交互操作**：流畅的用户交互，响应式设计

## 🔍 核心算法

### SKU匹配算法 (`modules/sku_matcher.py`)
- **模糊匹配**：基于编辑距离的智能匹配算法
- **多维度匹配**：款号、颜色、规格综合匹配评分
- **优先级排序**：按匹配度自动排序结果
- **缓存机制**：匹配结果缓存，提高查询效率

### 图像分割算法 (`modules/image_splitter.py`)
- **智能检测**：自动检测票据边界和内容区域
- **自适应分割**：根据内容密度调整分割策略
- **质量保证**：确保分割后图像的清晰度和完整性
- **自定义分割**：支持用户手动调整分割线位置

### AI解析算法 (`modules/ai_processor.py`)
- **多模型支持**：支持doubao、GPT等多种AI模型
- **智能提示**：优化的prompt模板，提高识别准确率
- **结果验证**：自动验证解析结果的合理性
- **错误处理**：完善的异常处理和重试机制

## 🏗️ 代码架构与逻辑

### 📋 主要业务流程

#### 1. 程序启动流程
```
main.py
├── 检查依赖 (check_dependencies)
├── 创建QApplication
├── 导入主界面 (modules.pyqt5_main_gui.ModernTicketProcessorGUI)
└── 显示主窗口 (showMaximized)

modules/pyqt5_main_gui.py (重构入口)
└── 导入 modules.gui.ModernTicketProcessorGUI

modules/gui/main_gui_controller.py (主控制器)
├── 初始化UI组件
├── 加载配置 (modules/config_manager.py)
├── 初始化各功能管理器
│   ├── image_manager.py (图像管理)
│   ├── table_ui_manager.py (表格管理)
│   ├── ai_processor_ui.py (AI处理界面)
│   ├── config_ui_manager.py (配置界面)
│   └── erp_ui_manager.py (ERP界面)
└── 设置事件处理 (event_handlers.py)
```

#### 2. AI解析流程
```
用户操作: 点击"AI解析票据"按钮
├── modules/gui/ai_processor_ui.py
│   ├── start_ai_processing_internal()
│   ├── 显示统一进度条 (main_gui_controller.show_unified_progress)
│   └── 启动AI处理线程
│
├── modules/threads/ai_processing_thread.py
│   ├── run() 方法执行
│   ├── 调用 modules/ai_processor.py
│   │   ├── analyze_ticket() 分析票据
│   │   ├── encode_image() 编码图像
│   │   ├── detect_api_type() 检测API类型
│   │   ├── build_request_payload() 构建请求
│   │   └── parse_ai_response() 解析响应
│   ├── 发送进度信号 (progress_updated)
│   └── 发送结果信号 (result_ready)
│
└── modules/gui/ai_processor_ui.py
    ├── on_ai_progress_updated() 更新进度
    ├── on_ai_result_ready() 处理结果
    ├── 更新表格数据 (table_ui_manager.add_result_to_table)
    └── 隐藏进度条 (main_gui_controller.hide_unified_progress)
```

#### 3. ERP查询流程
```
用户操作: 点击"查询ERP"按钮
├── modules/gui/erp_ui_manager.py
│   ├── start_erp_query() 启动查询
│   ├── 显示统一进度条
│   └── 启动ERP查询线程
│
├── modules/threads/erp_query_thread.py
│   ├── run() 方法执行
│   ├── 调用 modules/erp_integration.py
│   │   ├── check_auth_status() 检查认证
│   │   ├── query_product_skus() 查询商品
│   │   └── 使用 modules/sku_matcher.py 进行匹配
│   ├── 发送进度信号 (progress_updated)
│   └── 发送结果信号 (query_completed)
│
└── modules/gui/erp_ui_manager.py
    ├── _on_erp_query_progress() 更新进度
    ├── _on_erp_query_completed() 处理结果
    ├── 更新表格状态 (table_ui_manager.update_table_row_status)
    ├── 显示颜色确认面板 (color_confirm_panel.py)
    └── 隐藏进度条
```

#### 4. 颜色确认流程
```
用户操作: 在颜色确认面板选择颜色
├── modules/color_confirm_panel.py
│   ├── load_confirmation_data() 加载确认数据
│   ├── on_card_clicked() 处理卡片点击
│   ├── confirm_selection() 确认选择
│   └── 发送确认信号 (color_confirmed)
│
└── modules/gui/main_gui_controller.py
    ├── on_color_confirmed() 处理确认信号
    ├── 计算价格差异和利润
    ├── 生成状态文本 (📈 ¥X 或 📉 ¥X)
    ├── 更新表格状态 (带颜色显示)
    └── 保存状态到缓存 (erp_match_results)
```

#### 5. 成本价上传流程
```
用户操作: 点击"上传成本"按钮
├── modules/gui/erp_ui_manager.py
│   ├── start_cost_update() 启动更新
│   ├── 显示统一进度条
│   └── 启动成本更新线程
│
├── modules/threads/cost_update_thread.py
│   ├── run() 方法执行
│   ├── 调用 modules/erp_integration.py
│   │   └── update_cost_price() 更新成本价
│   ├── 生成状态文本 (📈 ¥X 或 📉 ¥X)
│   ├── 发送进度信号 (progress_updated)
│   └── 发送完成信号 (update_completed)
│
└── modules/gui/erp_ui_manager.py
    ├── _on_cost_update_progress() 更新进度
    ├── _on_cost_item_updated() 处理单项更新
    ├── 更新表格状态 (带颜色显示)
    ├── _on_cost_update_completed() 处理完成
    └── 隐藏进度条
```

### 📊 状态管理机制

#### 状态隔离架构
```
modules/gui/table_ui_manager.py
├── erp_states_cache = {}  # 文件级状态缓存
│   └── {file_path: {sku_key: {status, confirmed, profit_text}}}
├── restore_erp_states() 恢复状态
├── save_current_erp_states() 保存状态
└── clear_erp_states() 清理状态

状态键格式: "款号|颜色规格"
状态数据: {
    "status": "📈 ¥5",
    "confirmed": True,
    "profit_text": "¥20 (15.2%)",
    "color_groups": {...}
}
```

### 🎨 UI样式系统

#### 样式管理架构
```
modules/ui_style_manager.py
├── get_dark_theme_stylesheet() 深色主题样式
├── get_button_style() 按钮样式
└── 全局CSS样式定义

modules/ui_constants.py
├── Colors 颜色常量
├── Fonts 字体常量
├── Sizes 尺寸常量
└── Table 表格配置

样式冲突解决:
modules/gui/table_ui_manager.py
├── 删除CSS中的强制color设置
├── 使用setForeground()设置字体颜色
└── 状态颜色规则:
    ├── 📈 价格上涨: 红色 (#F44336)
    ├── 📉 价格下降: 绿色 (#4CAF50)
    ├── ✅ 成功状态: 绿色 (#4CAF50)
    ├── 🟡 待确认: 橙色 (#FF9800)
    └── ❌ 错误状态: 红色 (#F44336)
```

### 🔧 配置管理系统

#### 配置文件结构
```
modules/config_manager.py
├── load_config() 加载配置
├── save_config() 保存配置
└── 配置文件映射:
    ├── user_config.json (AI配置、UI设置)
    ├── latest_cookies.json (ERP认证)
    ├── suppliers.json (供应商数据)
    └── file_manager.json (文件管理)

配置界面管理:
modules/gui/config_ui_manager.py
├── AI配置面板 (API密钥、模型选择)
├── ERP配置面板 (Cookie管理)
├── 供应商配置面板
└── UI设置面板
```

## 🐛 故障排除

### 常见问题及解决方案

#### ❌ Token失效错误
**问题**：显示"Token失效，需要重新登录"
**解决**：
1. 检查网络连接状态
2. 更新 `latest_cookies.json` 中的Cookie信息
3. 确认ERP系统访问权限

#### ❌ AI解析失败
**问题**：AI解析返回错误或结果不准确
**解决**：
1. 检查AI API密钥是否有效
2. 确认API接口地址正确
3. 检查图像质量和清晰度
4. 尝试调整AI模型参数

#### ❌ 程序启动失败
**问题**：程序无法正常启动
**解决**：
1. 确认Python版本（需要3.8+）
2. 重新安装依赖：`pip install -r requirements.txt`
3. 检查系统权限和防火墙设置

#### ⚠️ 字体警告信息
**问题**：控制台显示字体枚举警告
**解决**：这是正常现象，不影响程序功能，可以忽略

#### ❌ 进度条颜色不显示
**问题**：成本价上传后状态颜色不正确
**解决**：
1. 已修复CSS样式冲突问题
2. 删除了强制的color设置
3. 使用setForeground()正确设置字体颜色
4. 重启程序查看效果

#### ❌ 状态数据丢失
**问题**：切换文件后ERP状态丢失
**解决**：
1. 已实现完整的状态隔离机制
2. 基于文件路径的状态缓存
3. 自动保存和恢复状态
4. 避免数据污染

## 📈 性能特点

### 🚀 高性能架构
- **模块化设计**：7个专门模块，代码从6557行重构为清晰的模块结构
- **多线程处理**：AI解析、ERP查询、成本更新三个独立线程并行处理
- **内存优化**：智能缓存管理，自动清理临时文件，减少内存占用
- **响应式界面**：统一进度条反馈，流畅的用户交互体验

### 📊 处理能力
- **批量处理**：支持同时处理多张票据，状态完全隔离
- **快速匹配**：基于编辑距离的高效SKU匹配算法
- **实时更新**：即时的状态反馈和结果显示，支持增量更新
- **状态持久化**：完整的状态保存和恢复机制，避免数据丢失

### 🔧 技术优化
- **CSS样式冲突解决**：解决了setForeground()被CSS覆盖的问题
- **进度条统一**：三种操作使用统一的底部进度条显示
- **状态颜色优化**：价格变化直观显示，删除冗余文字描述
- **自动清理机制**：程序关闭时自动清理temp_segments临时文件

## 🔒 安全特性

### 数据安全
- **本地存储**：敏感配置信息本地加密存储
- **访问控制**：ERP系统访问权限验证
- **数据备份**：重要操作自动备份

### 系统安全
- **异常处理**：完善的错误处理和恢复机制
- **日志记录**：详细的操作日志和错误追踪
- **版本控制**：代码版本管理和回滚支持

## 📝 开发说明

### 技术栈
- **界面框架**：PyQt5 (主界面、组件、事件处理)
- **图像处理**：PIL (Python Imaging Library) - 图像编码、分割、预览
- **HTTP请求**：requests - AI API调用、ERP接口通信
- **数据处理**：json (配置管理)、re (正则匹配)
- **多线程**：QThread - AI处理、ERP查询、成本更新线程
- **样式管理**：CSS样式表 + PyQt5样式系统

### 代码规范
- **模块化设计**：7个专门模块，功能明确分离，职责单一
- **重构优化**：从单文件6557行重构为模块化结构
- **类型注解**：关键方法提供类型提示
- **文档注释**：详细的函数和类文档，包含参数说明
- **错误处理**：完善的异常捕获和处理，线程安全日志

### 关键设计模式
- **MVC模式**：Model(数据) - View(界面) - Controller(控制器)分离
- **观察者模式**：信号槽机制，线程间通信
- **单例模式**：配置管理器、日志管理器
- **工厂模式**：AI处理器、ERP集成器的创建

## 📂 详细文件路径与功能对应

### 🎯 核心业务逻辑文件

| 文件路径 | 主要功能 | 关键方法 |
|---------|---------|---------|
| `modules/ai_processor.py` | AI图像解析核心 | `analyze_ticket()`, `process_image()`, `parse_ai_response()` |
| `modules/erp_integration.py` | ERP系统集成接口 | `query_product_skus()`, `update_cost_price()`, `check_auth_status()` |
| `modules/sku_matcher.py` | SKU智能匹配算法 | `find_best_matches()`, `calculate_similarity()` |
| `modules/color_confirm_panel.py` | 颜色规格确认面板 | `load_confirmation_data()`, `confirm_selection()` |
| `modules/config_manager.py` | 配置文件管理 | `load_config()`, `save_config()` |
| `modules/file_manager.py` | 文件操作管理 | 文件上传、保存、路径管理 |
| `modules/image_splitter.py` | 图像分割处理 | 智能分割、自定义分割线 |
| `modules/supplier_manager.py` | 供应商信息管理 | 供应商数据库操作 |
| `modules/table_manager.py` | 表格数据管理 | 数据模型、状态管理 |

### 🖥️ GUI界面管理文件

| 文件路径 | 主要功能 | 关键方法 |
|---------|---------|---------|
| `modules/gui/main_gui_controller.py` | 主界面控制器 | `setup_ui()`, `show_unified_progress()`, `on_color_confirmed()` |
| `modules/gui/image_manager.py` | 图像管理器 | 图像预览、缩略图、全屏显示 |
| `modules/gui/table_ui_manager.py` | 表格界面管理 | `update_table_row_status()`, `restore_erp_states()` |
| `modules/gui/ai_processor_ui.py` | AI处理界面 | `start_ai_processing_internal()`, `on_ai_progress_updated()` |
| `modules/gui/config_ui_manager.py` | 配置界面管理 | AI配置、ERP配置、供应商配置界面 |
| `modules/gui/erp_ui_manager.py` | ERP界面管理 | `start_erp_query()`, `start_cost_update()` |
| `modules/gui/event_handlers.py` | 事件处理器 | 用户交互事件处理 |
| `modules/gui/image_card_widget.py` | 图像卡片组件 | 缩略图显示组件 |
| `modules/gui/image_split_overlay.py` | 图像分割覆盖层 | 分割线管理和显示 |
| `modules/gui/cookies_manager_widget.py` | Cookie管理组件 | ERP认证Cookie管理 |

### 🧵 多线程处理文件

| 文件路径 | 主要功能 | 关键方法 |
|---------|---------|---------|
| `modules/threads/ai_processing_thread.py` | AI处理线程 | `run()`, 发送`progress_updated`和`result_ready`信号 |
| `modules/threads/erp_query_thread.py` | ERP查询线程 | `run()`, 发送`progress_updated`和`query_completed`信号 |
| `modules/threads/cost_update_thread.py` | 成本更新线程 | `run()`, 发送`progress_updated`和`update_completed`信号 |
| `modules/threads/thread_safe_logger.py` | 线程安全日志 | 多线程环境下的安全日志记录 |

### 🎨 样式和常量文件

| 文件路径 | 主要功能 | 关键内容 |
|---------|---------|---------|
| `modules/ui_style_manager.py` | UI样式管理 | `get_dark_theme_stylesheet()`, 深色主题CSS |
| `modules/ui_constants.py` | UI常量定义 | 颜色常量、尺寸常量、表格配置 |

### 🛠️ 工具和实用文件

| 文件路径 | 主要功能 | 关键方法 |
|---------|---------|---------|
| `modules/utils/logger.py` | 日志管理工具 | 统一日志接口和格式化 |

### 📄 配置和数据文件

| 文件路径 | 数据类型 | 主要内容 |
|---------|---------|---------|
| `user_config.json` | 用户配置 | AI API配置、UI设置、温度参数等 |
| `latest_cookies.json` | ERP认证 | ERP系统访问Cookie和认证信息 |
| `suppliers.json` | 供应商数据 | 供应商名称、编码、联系方式 |
| `file_manager.json` | 文件管理 | 文件路径、处理状态、缓存信息 |

### 🔄 信号槽连接关系

#### AI处理信号流
```
AIProcessingThread (modules/threads/ai_processing_thread.py)
├── progress_updated → ai_processor_ui.on_ai_progress_updated()
├── result_ready → ai_processor_ui.on_ai_result_ready()
└── finished → ai_processor_ui.on_ai_processing_finished()
```

#### ERP查询信号流
```
ERPQueryThread (modules/threads/erp_query_thread.py)
├── progress_updated → erp_ui_manager._on_erp_query_progress()
├── query_completed → erp_ui_manager._on_erp_query_completed()
└── finished → erp_ui_manager._on_erp_query_finished()
```

#### 成本更新信号流
```
CostUpdateThread (modules/threads/cost_update_thread.py)
├── progress_updated → erp_ui_manager._on_cost_update_progress()
├── item_updated → erp_ui_manager._on_cost_item_updated()
└── update_completed → erp_ui_manager._on_cost_update_completed()
```

#### 颜色确认信号流
```
ColorConfirmPanel (modules/color_confirm_panel.py)
└── color_confirmed → main_gui_controller.on_color_confirmed()
```

---

## 🎉 更新日志

### v2.0 重构版本
- ✅ **模块化重构**：从单文件6557行重构为7个专门模块
- ✅ **状态管理优化**：完整的状态隔离和持久化机制
- ✅ **进度条统一**：三种操作使用统一的底部进度条
- ✅ **样式冲突修复**：解决了字体颜色显示问题
- ✅ **状态颜色优化**：价格变化直观显示，删除冗余文字
- ✅ **自动清理机制**：程序关闭时自动清理临时文件
- ✅ **图像分割增强**：支持自定义分割线和智能分割
- ✅ **Cookie管理优化**：完善的ERP认证管理

### 技术改进
- 🔧 **架构优化**：MVC模式，观察者模式，模块化设计
- 🔧 **性能提升**：多线程处理，智能缓存，内存优化
- 🔧 **用户体验**：统一进度反馈，直观状态显示，流畅交互
- 🔧 **代码质量**：完善的错误处理，详细的文档注释

---

**智能票据处理系统 v2.0** - 让票据处理和成本管理变得简单高效！ 🎯

> 📋 **完整技术文档**：[项目技术文档.md](./项目技术文档.md)
> 🖼️ **图像分割说明**：[自定义图像分割功能说明.md](./自定义图像分割功能说明.md)
> 🔗 **ERP接口文档**：[ERP_API_文档.md](./ERP_API_文档.md)
