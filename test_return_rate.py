#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试退货率计算功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.return_rate_calculator import ReturnRateCalculator

def test_return_rate_functionality():
    """测试退货率计算功能"""
    print("🧪 开始测试退货率计算功能...")
    
    calculator = ReturnRateCalculator()
    
    # 测试数据：GT217-7528款号下的12个SKU
    test_skus = [
        {"sku_id": "GT217-7528-红色-S", "sent_qty_7": 5, "as_qty_7": 4, "sent_qty_15": 8, "as_qty_15": 6, "sent_qty_30": 12, "as_qty_30": 8},
        {"sku_id": "GT217-7528-红色-M", "sent_qty_7": 6, "as_qty_7": 5, "sent_qty_15": 10, "as_qty_15": 7, "sent_qty_30": 15, "as_qty_30": 10},
        {"sku_id": "GT217-7528-红色-L", "sent_qty_7": 7, "as_qty_7": 6, "sent_qty_15": 12, "as_qty_15": 9, "sent_qty_30": 18, "as_qty_30": 12},
        {"sku_id": "GT217-7528-红色-XL", "sent_qty_7": 3, "as_qty_7": 2, "sent_qty_15": 5, "as_qty_15": 3, "sent_qty_30": 8, "as_qty_30": 5},
        {"sku_id": "GT217-7528-蓝色-S", "sent_qty_7": 4, "as_qty_7": 3, "sent_qty_15": 7, "as_qty_15": 5, "sent_qty_30": 11, "as_qty_30": 7},
        {"sku_id": "GT217-7528-蓝色-M", "sent_qty_7": 5, "as_qty_7": 4, "sent_qty_15": 9, "as_qty_15": 6, "sent_qty_30": 14, "as_qty_30": 9},
        {"sku_id": "GT217-7528-蓝色-L", "sent_qty_7": 6, "as_qty_7": 5, "sent_qty_15": 11, "as_qty_15": 8, "sent_qty_30": 17, "as_qty_30": 11},
        {"sku_id": "GT217-7528-蓝色-XL", "sent_qty_7": 2, "as_qty_7": 2, "sent_qty_15": 4, "as_qty_15": 2, "sent_qty_30": 6, "as_qty_30": 4},
        {"sku_id": "GT217-7528-黑色-S", "sent_qty_7": 3, "as_qty_7": 3, "sent_qty_15": 6, "as_qty_15": 4, "sent_qty_30": 9, "as_qty_30": 6},
        {"sku_id": "GT217-7528-黑色-M", "sent_qty_7": 4, "as_qty_7": 3, "sent_qty_15": 8, "as_qty_15": 5, "sent_qty_30": 12, "as_qty_30": 8},
        {"sku_id": "GT217-7528-黑色-L", "sent_qty_7": 5, "as_qty_7": 4, "sent_qty_15": 10, "as_qty_15": 7, "sent_qty_30": 15, "as_qty_30": 10},
        {"sku_id": "GT217-7528-黑色-XL", "sent_qty_7": 1, "as_qty_7": 1, "sent_qty_15": 3, "as_qty_15": 2, "sent_qty_30": 5, "as_qty_30": 3},
    ]
    
    print(f"📊 测试数据：{len(test_skus)} 个SKU")
    
    # 计算汇总数据
    total_sent_7 = sum(sku.get("sent_qty_7", 0) for sku in test_skus)
    total_returned_7 = sum(sku.get("as_qty_7", 0) for sku in test_skus)
    total_sent_15 = sum(sku.get("sent_qty_15", 0) for sku in test_skus)
    total_returned_15 = sum(sku.get("as_qty_15", 0) for sku in test_skus)
    total_sent_30 = sum(sku.get("sent_qty_30", 0) for sku in test_skus)
    total_returned_30 = sum(sku.get("as_qty_30", 0) for sku in test_skus)
    
    print(f"📈 7天汇总：实发 {total_sent_7} 件，实退 {total_returned_7} 件")
    print(f"📈 15天汇总：实发 {total_sent_15} 件，实退 {total_returned_15} 件")
    print(f"📈 30天汇总：实发 {total_sent_30} 件，实退 {total_returned_30} 件")
    
    # 计算预期退货率
    expected_rate_7 = (total_returned_7 / total_sent_7) * 100 if total_sent_7 > 0 else 0
    expected_rate_15 = (total_returned_15 / total_sent_15) * 100 if total_sent_15 > 0 else 0
    expected_rate_30 = (total_returned_30 / total_sent_30) * 100 if total_sent_30 > 0 else 0
    
    print(f"🎯 预期退货率：")
    print(f"   - 7天: {expected_rate_7:.2f}%")
    print(f"   - 15天: {expected_rate_15:.2f}%")
    print(f"   - 30天: {expected_rate_30:.2f}%")
    
    # 使用计算器计算退货率
    return_rates = calculator.calculate_return_rates_by_sku_code(test_skus)
    print(f"🧮 计算器结果: {return_rates}")
    
    # 获取摘要信息
    summary = calculator.get_return_rate_summary(return_rates)
    print(f"📋 摘要信息:")
    print(f"   - 显示文本: {summary['display_text']}")
    print(f"   - 颜色: {summary['color']}")
    print(f"   - 等级: {summary['level']}")
    print(f"   - 主要退货率: {summary['main_rate']}")
    
    # 验证计算结果
    print(f"\n✅ 验证结果:")
    tolerance = 0.01  # 允许的误差范围
    
    for period, expected in [("7天", expected_rate_7), ("15天", expected_rate_15), ("30天", expected_rate_30)]:
        calculated = return_rates.get(period, 0)
        diff = abs(calculated - expected)
        if diff <= tolerance:
            print(f"   ✅ {period}: 计算正确 ({calculated:.2f}% vs {expected:.2f}%)")
        else:
            print(f"   ❌ {period}: 计算错误 ({calculated:.2f}% vs {expected:.2f}%, 差异: {diff:.2f}%)")
    
    # 测试颜色判断
    print(f"\n🎨 颜色判断测试:")
    test_rates = [
        {"7天": 15.5},  # 应该是绿色
        {"7天": 35.2},  # 应该是橙色
        {"7天": 65.8},  # 应该是红色
        {}              # 应该是白色
    ]
    
    for i, rates in enumerate(test_rates):
        color = calculator.get_return_rate_color(rates)
        rate = rates.get("7天", -1)
        if rate < 0:
            expected_color = "#FFFFFF"
            level = "无数据"
        elif rate < 20:
            expected_color = "#4CAF50"
            level = "低退货率"
        elif rate < 50:
            expected_color = "#FF9800"
            level = "中等退货率"
        else:
            expected_color = "#F44336"
            level = "高退货率"
        
        print(f"   测试{i+1}: {rate}% -> {color} ({level})")
        if color == expected_color:
            print(f"      ✅ 颜色正确")
        else:
            print(f"      ❌ 颜色错误，预期: {expected_color}")
    
    print(f"\n🎉 退货率计算功能测试完成！")

if __name__ == "__main__":
    test_return_rate_functionality()
