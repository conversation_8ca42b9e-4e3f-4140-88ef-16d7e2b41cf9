# 自定义图像分割功能说明

## 功能概述

为您的智能票据处理系统添加了全新的**自定义图像分割功能**，支持交互式的可拖拽分割线，让您能够精确控制图像分割位置。

## 功能特点

### 🎯 核心功能
- **可视化分割线**：2px红色分割线，清晰可见
- **拖拽调整**：鼠标拖拽调整分割线位置
- **智能约束**：分割线不能超出图像边界，保持最小距离
- **实时预览**：拖拽时实时显示分割效果

### 🎨 用户界面
- **顶部工具栏**：显示功能标题和退出按钮
- **图像显示区域**：支持缩放和滚动的图像预览
- **底部悬浮按钮**：添加分割线、删除分割线、确认分割

### 🔧 操作功能
- **添加分割线**：智能寻找最佳位置添加新分割线
- **删除分割线**：删除最后添加的分割线
- **确认分割**：执行分割并保存结果文件

## 使用方法

### 1. 启动分割功能
1. 在图像预览界面，双击图像进入全屏预览模式
2. 点击顶部的 **"🔪 自定义分割"** 按钮
3. 系统将打开自定义分割窗口

### 2. 调整分割线
1. **默认分割线**：系统自动添加2条分割线，将图像三等分
2. **拖拽调整**：鼠标悬停在分割线上，光标变为调整样式，拖拽调整位置
3. **添加分割线**：点击底部 **"➕ 添加分割线"** 按钮
4. **删除分割线**：点击底部 **"➖ 删除分割线"** 按钮

### 3. 完成分割
1. 调整满意后，点击 **"✓ 确认分割"** 按钮
2. 系统将根据分割线位置生成多个图像文件
3. 分割后的文件保存在 `temp_segments` 目录中
4. 文件命名格式：`原文件名_segment_1.jpg`、`原文件名_segment_2.jpg` 等

### 4. 取消分割
- 点击顶部 **"✕ 退出分割"** 按钮可随时取消操作

## 技术特性

### 🔒 安全约束
- **边界限制**：分割线不能拖拽到图像边界之外
- **最小距离**：分割线之间保持50像素最小距离，避免重叠
- **智能定位**：新增分割线时自动寻找最佳位置

### 📁 文件管理
- **输出目录**：`temp_segments/`
- **文件格式**：JPEG格式，95%质量
- **命名规则**：`原文件名_segment_序号.jpg`
- **自动集成**：分割后的文件自动添加到文件管理器

### 🎨 界面设计
- **现代化UI**：黑色主题，与系统整体风格一致
- **悬浮按钮**：底部透明悬浮按钮，不遮挡图像内容
- **响应式布局**：支持窗口大小调整，自动适配

## 文件结构

```
modules/gui/
├── custom_image_splitter.py    # 自定义分割组件
├── image_manager.py           # 图像管理器（已更新）
└── ...

temp_segments/                 # 分割结果输出目录
├── 图像1_segment_1.jpg
├── 图像1_segment_2.jpg
└── ...
```

## 测试方法

### 独立测试
运行测试脚本：
```bash
python test_custom_splitter.py
```

### 集成测试
1. 启动主程序：`python main.py`
2. 选择图像文件
3. 双击进入全屏预览
4. 点击"自定义分割"按钮测试功能

## 注意事项

### ⚠️ 使用建议
1. **图像尺寸**：建议使用高度大于300像素的图像，效果更佳
2. **分割数量**：建议分割线数量不超过10条，避免片段过小
3. **文件格式**：支持常见图像格式（JPG、PNG、BMP等）

### 🔧 故障排除
1. **分割线无法拖拽**：检查鼠标是否悬停在分割线上
2. **分割失败**：检查原图像文件是否完整，输出目录是否有写入权限
3. **界面显示异常**：尝试调整窗口大小或重新加载图像

## 更新日志

### v1.0.0 (2025-06-25)
- ✅ 实现基础自定义分割功能
- ✅ 支持可拖拽分割线
- ✅ 集成到主界面图像预览
- ✅ 添加底部悬浮操作按钮
- ✅ 实现智能分割线定位
- ✅ 支持分割结果自动集成

---

**开发者**: 一介哥专用AI助手  
**版本**: v1.0.0  
**更新时间**: 2025-06-25
