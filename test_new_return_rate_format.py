#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的退货率格式化功能
验证三列显示格式和整数显示
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.return_rate_calculator import ReturnRateCalculator

def test_new_format():
    """测试新的退货率格式化功能"""
    print("🧪 测试新的退货率格式化功能")
    print("=" * 50)
    
    # 创建计算器
    calculator = ReturnRateCalculator()
    
    # 测试数据
    test_skus = [
        {
            "sku_code": "GT231-1100-粉色-S",
            "sent_qty_7": 15,
            "as_qty_7": 12,
            "sent_qty_15": 20,
            "as_qty_15": 15,
            "sent_qty_30": 25,
            "as_qty_30": 16
        },
        {
            "sku_code": "GT231-1100-粉色-M",
            "sent_qty_7": 26,
            "as_qty_7": 22,
            "sent_qty_15": 30,
            "as_qty_15": 21,
            "sent_qty_30": 35,
            "as_qty_30": 23
        }
    ]
    
    # 计算退货率
    return_rates = calculator.calculate_return_rates_by_sku_code(test_skus)
    print(f"📊 计算的退货率: {return_rates}")
    
    # 测试旧格式（用于兼容性）
    old_format = calculator.format_return_rate_display(return_rates)
    print(f"📝 旧格式显示: {old_format}")
    
    # 测试新格式（三列整数显示）
    new_format = calculator.format_return_rate_integer(return_rates)
    print(f"🆕 新格式显示: {new_format}")
    
    # 验证格式
    print("\n✅ 验证结果:")
    for period, rate_text in new_format.items():
        if rate_text != "-":
            # 检查是否为整数格式
            if rate_text.endswith("%") and rate_text[:-1].isdigit():
                print(f"   ✅ {period}: {rate_text} (整数格式正确)")
            else:
                print(f"   ❌ {period}: {rate_text} (格式错误)")
        else:
            print(f"   ⚪ {period}: {rate_text} (无数据)")
    
    # 测试颜色逻辑
    summary = calculator.get_return_rate_summary(return_rates)
    print(f"\n🎨 颜色信息:")
    print(f"   - 主要退货率: {summary['main_rate']:.1f}%")
    print(f"   - 颜色代码: {summary['color']}")
    print(f"   - 等级: {summary['level']}")
    
    return True

def test_edge_cases():
    """测试边界情况"""
    print("\n🧪 测试边界情况")
    print("=" * 50)
    
    calculator = ReturnRateCalculator()
    
    # 测试空数据
    empty_rates = calculator.format_return_rate_integer({})
    print(f"📝 空数据: {empty_rates}")
    
    # 测试无效数据
    invalid_rates = calculator.format_return_rate_integer({"7天": -1, "15天": -1, "30天": -1})
    print(f"📝 无效数据: {invalid_rates}")
    
    # 测试极值
    extreme_rates = calculator.format_return_rate_integer({"7天": 0.4, "15天": 99.6, "30天": 50.5})
    print(f"📝 极值数据: {extreme_rates}")
    
    return True

if __name__ == "__main__":
    print("🚀 开始测试新的退货率格式化功能")
    
    try:
        test_new_format()
        test_edge_cases()
        print("\n🎉 所有测试通过！")
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
