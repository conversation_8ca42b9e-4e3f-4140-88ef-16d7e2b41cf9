# 界面布局和退货率显示优化说明

## 📋 修改概述

本次修改主要实现了两个核心功能：
1. **左侧面板固定宽度**：将左侧图像预览面板固定为700px宽度
2. **退货率三列显示**：将原来的单列退货率拆分为"7退"、"15退"、"30退"三列，并只显示整数百分比

## 🔧 具体修改内容

### 1. UI常量配置修改 (`modules/ui_constants.py`)

#### 分割器尺寸调整
```python
# 修改前
MAIN_SPLITTER_SIZES = [600, 850, 750]  # 左侧图像预览:中间表格:右侧确认

# 修改后  
MAIN_SPLITTER_SIZES = [700, 800, 700]  # 左侧图像预览:中间表格:右侧确认 (左侧固定700px)
```

#### 表格列头更新
```python
# 修改前
PICKUP_HEADERS = ["行数", "款号", "颜色规格", "数量", "单价", "小计", "状态", "利润", "退货率"]
RETURN_HEADERS = ["行数", "款号", "颜色规格", "数量", "单价", "小计", "状态", "利润", "退货率"]

# 修改后
PICKUP_HEADERS = ["行数", "款号", "颜色规格", "数量", "单价", "小计", "状态", "利润", "7退", "15退", "30退"]
RETURN_HEADERS = ["行数", "款号", "颜色规格", "数量", "单价", "小计", "状态", "利润", "7退", "15退", "30退"]
```

### 2. 退货率计算器增强 (`modules/return_rate_calculator.py`)

#### 新增整数格式化方法
```python
def format_return_rate_integer(self, return_rates: Dict[str, float]) -> Dict[str, str]:
    """
    格式化退货率为整数显示（用于表格三列显示）
    
    Args:
        return_rates: 退货率字典
        
    Returns:
        格式化的退货率字典，如 {"7天": "82%", "15天": "76%", "30天": "68%"}
    """
    if not return_rates:
        return {"7天": "-", "15天": "-", "30天": "-"}
    
    result = {}
    for period in ["7天", "15天", "30天"]:
        if period in return_rates:
            rate = return_rates[period]
            if rate >= 0:  # 只显示有效的退货率
                result[period] = f"{int(round(rate))}%"
            else:
                result[period] = "-"
        else:
            result[period] = "-"
    
    return result
```

### 3. 表格管理器更新 (`modules/table_manager.py`)

#### 退货率设置方法重构
- `set_return_rate()`: 更新为三列格式设置
- `set_return_rate_direct()`: 支持直接设置三列退货率
- `restore_table_data()`: 兼容旧格式数据的恢复

#### 关键特性
- **三列显示**：第8列(7退)、第9列(15退)、第10列(30退)
- **整数格式**：显示如"82%"而不是"82.3%"
- **颜色编码**：
  - 🟢 绿色：< 20%（优秀）
  - 🟠 橙色：20-50%（一般）
  - 🔴 红色：> 50%（需关注）

### 4. 主界面控制器优化 (`modules/gui/main_gui_controller.py`)

#### 左侧面板固定宽度设置
```python
# 设置左侧面板固定宽度为700px
if main_splitter.count() > 0:
    left_widget = main_splitter.widget(0)
    if left_widget:
        left_widget.setFixedWidth(700)
        # 设置分割器的拉伸因子
        main_splitter.setStretchFactor(0, 0)  # 左侧不拉伸（固定宽度）
        main_splitter.setStretchFactor(1, 1)  # 中间表格区域可拉伸
        main_splitter.setStretchFactor(2, 0)  # 右侧固定比例
```

#### 退货率计算逻辑更新
- 颜色确认时的退货率计算改为三列格式
- ERP查询完成后的退货率更新改为三列格式

### 5. ERP界面管理器更新 (`modules/gui/erp_ui_manager.py`)

#### 退货率更新逻辑
- `_calculate_and_update_return_rate()`: 更新为三列格式
- 保持数据兼容性，同时支持新旧格式

### 6. 表格UI管理器更新 (`modules/gui/table_ui_manager.py`)

#### 表格属性设置
- 更新列数为11列（增加2列退货率）
- 状态恢复时支持新旧格式兼容

## 🎯 功能效果

### 界面布局优化
1. **左侧面板固定700px**：图像预览区域宽度固定，不会因窗口调整而变化
2. **右侧表格自动扩展**：表格区域获得更多空间，解决列内容显示不全的问题
3. **响应式设计**：窗口大小变化时，中间表格区域自动调整

### 退货率显示优化
1. **三列独立显示**：
   - 7退：7天退货率
   - 15退：15天退货率  
   - 30退：30天退货率

2. **整数格式**：
   - 修改前：`7天:82.9% | 15天:75.5% | 30天:68.2%`
   - 修改后：`82%` `76%` `68%`（分别在三列中显示）

3. **颜色编码保持**：
   - 绿色：退货率 < 20%
   - 橙色：退货率 20-50%
   - 红色：退货率 > 50%

## 🔄 兼容性处理

### 数据格式兼容
- 支持旧格式退货率数据的自动转换
- 新旧格式数据可以无缝切换
- 保持现有功能的完整性

### 界面兼容
- 保持原有的操作流程不变
- 商品卡片中仍显示7天退货率（简化格式）
- 所有现有功能正常工作

## 🧪 测试验证

创建了测试文件 `test_new_return_rate_format.py` 验证：
- ✅ 新格式化方法正确工作
- ✅ 整数显示格式正确
- ✅ 边界情况处理正常
- ✅ 颜色编码逻辑正确

## 📝 使用说明

### 用户体验改进
1. **更清晰的数据展示**：三列退货率一目了然
2. **更大的表格空间**：左侧固定宽度释放更多空间给表格
3. **简化的数字格式**：整数百分比更易读

### 操作流程不变
- ERP查询流程保持不变
- 颜色确认流程保持不变
- 数据导出和保存功能正常

## 🎉 总结

本次优化成功实现了：
- ✅ 左侧面板固定700px宽度
- ✅ 退货率三列显示（7退、15退、30退）
- ✅ 整数百分比格式（如82%）
- ✅ 表格空间优化
- ✅ 完整的向后兼容性
- ✅ 所有现有功能保持正常

用户现在可以享受更清晰的界面布局和更直观的退货率显示！
