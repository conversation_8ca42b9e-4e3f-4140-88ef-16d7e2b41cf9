# ERP系统API接口文档

## 📋 概述

本文档详细说明了ERP321系统的商品成本价管理API接口，包括查询和更新功能的完整实现方案。

## 🔧 系统信息

- **ERP系统**: ERP321 (erp321.com)
- **API基础地址**: `https://apiweb.erp321.com/webapi`
- **前端地址**: `https://src.erp321.com`
- **公司ID**: `13881863`
- **用户ID**: `18707109`

## 🔐 认证方式

### Cookie认证
ERP系统使用Cookie进行用户认证，主要包含以下关键Cookie：

```
关键Cookie字段：
- _sid18707109: 会话ID
- u_sso_token: 单点登录令牌
- u_id: 用户ID
- u_co_id: 公司ID
- isLogin: 登录状态标识
```

### 认证状态检查
- Cookie有效期：通常24小时
- 失效标识：API返回空数据或认证错误
- 刷新方式：重新登录获取新Cookie

## 📡 API接口详情

### 1. 商品信息查询接口

#### 接口地址
```
POST https://apiweb.erp321.com/webapi/ItemApi/ItemSku/GetPageListV2
```

#### 请求参数
```json
{
  "ip": "",
  "uid": "18707109",
  "coid": "13881863",
  "data": {
    "page": 1,
    "limit": 20,
    "search_text": "GD340-1011",
    "search_type": "sku_code",
    "sort_field": "",
    "sort_type": "",
    "filter": {}
  }
}
```

#### 响应格式
```json
{
  "page": {
    "currentPage": 1,
    "pageSize": 25,
    "count": 10,
    "pages": 1,
    "index": 0
  },
  "code": 0,
  "act": 0,
  "data": [
    {
      "sku_id": "GD340-1011-衬衫-L",
      "sku_code": "GD340-1011",
      "cost_price": 45.50,
      "item_name": "商品名称",
      "spec": "规格信息"
    }
  ],
  "msg": null,
  "msgType": "string",
  "requestId": "xxx",
  "cookie": null
}
```

### 2. 成本价更新接口 ⭐

#### 接口地址
```
POST https://apiweb.erp321.com/webapi/ItemApi/ItemSku/SetEditOrCreateItemSkuInfoTotal
```

#### 请求参数
```json
{
  "ip": "",
  "uid": "18707109",
  "coid": "13881863",
  "data": {
    "cost_price": 88.88,
    "sku_id": "GD340-9026-衬衫-L",
    "EditFlds": []
  }
}
```

#### 响应格式
```json
{
  "code": 0,
  "act": 0,
  "data": true,
  "msg": null,
  "msgType": "string",
  "requestId": "xxx",
  "cookie": null
}
```

#### 成功判断
- `code == 0` 且 `data == true` 表示更新成功
- 其他情况均为失败

## 🛠️ 技术实现

### Python实现示例

```python
import requests
import json

class ERPCostUpdater:
    def __init__(self):
        self.base_url = "https://apiweb.erp321.com/webapi"
        self.owner_co_id = "13881863"
        self.authorize_co_id = "13881863"
        self.cookies = {
            # 从浏览器获取的完整Cookie
        }
        self.headers = {
            'accept': 'application/json',
            'content-type': 'application/json; charset=utf-8',
            'origin': 'https://src.erp321.com',
            # 其他必要的请求头
        }
    
    def update_cost_price(self, sku_id: str, new_cost_price: float) -> bool:
        """更新商品成本价"""
        update_url = f"{self.base_url}/ItemApi/ItemSku/SetEditOrCreateItemSkuInfoTotal"
        params = {
            'owner_co_id': self.owner_co_id,
            'authorize_co_id': self.authorize_co_id
        }
        
        update_data = {
            "ip": "",
            "uid": "18707109",
            "coid": "13881863",
            "data": {
                "cost_price": new_cost_price,
                "sku_id": sku_id,
                "EditFlds": []
            }
        }
        
        try:
            response = requests.post(
                update_url,
                params=params,
                headers=self.headers,
                cookies=self.cookies,
                json=update_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get('code') == 0 and result.get('data') is True
            return False
            
        except Exception as e:
            print(f"更新异常: {str(e)}")
            return False
```

## ⚠️ 重要注意事项

### 1. 认证相关
- **Cookie时效性**: Cookie通常24小时内有效，需要定期更新
- **登录状态**: 建议在每次API调用前检查认证状态
- **安全性**: 不要在代码中硬编码敏感的Cookie信息

### 2. API调用限制
- **频率限制**: 建议每次调用间隔至少1秒，避免触发限流
- **超时设置**: 建议设置30秒超时时间
- **重试机制**: 失败时建议重试1-2次

### 3. 数据格式要求
- **SKU ID格式**: 必须使用完整的SKU ID，如 "GD340-9026-衬衫-L"
- **价格格式**: 支持小数点后2位，如 88.88
- **字符编码**: 使用UTF-8编码

### 4. 错误处理
- **网络异常**: 处理连接超时、网络中断等情况
- **认证失效**: 检测到认证失效时自动重新登录
- **数据验证**: 在发送请求前验证数据格式

### 5. 性能优化
- **批量操作**: 对于大量数据，建议分批处理
- **连接复用**: 使用Session对象复用HTTP连接
- **缓存机制**: 对查询结果进行适当缓存

## 🔄 集成建议

### 1. 认证管理
```python
# 建议实现自动登录功能
def auto_login():
    # 使用Playwright自动登录获取Cookie
    pass

# 检查认证状态
def check_auth_status():
    # 发送测试请求检查Cookie是否有效
    pass
```

### 2. 批量处理
```python
def batch_update_cost_prices(updates: list):
    """批量更新成本价"""
    results = {}
    for update in updates:
        # 添加延时避免频率限制
        time.sleep(1)
        result = update_cost_price(update['sku_id'], update['cost_price'])
        results[update['sku_id']] = result
    return results
```

### 3. 错误重试
```python
def update_with_retry(sku_id: str, cost_price: float, max_retries: int = 3):
    """带重试的更新功能"""
    for attempt in range(max_retries):
        try:
            return update_cost_price(sku_id, cost_price)
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            time.sleep(2 ** attempt)  # 指数退避
```

## 📊 测试验证

### 测试用例
1. **单个商品更新**: 使用已知SKU ID测试单个更新
2. **批量更新**: 测试多个商品同时更新
3. **错误处理**: 测试无效SKU ID、网络异常等情况
4. **认证失效**: 测试Cookie过期时的处理

### 验证方法
- 通过ERP系统前端界面确认更新结果
- 对比API返回的成功状态
- 检查日志记录的详细信息

## 🚀 部署建议

1. **环境配置**: 确保Python环境安装了requests库
2. **配置管理**: 将Cookie等敏感信息存储在配置文件中
3. **日志记录**: 记录所有API调用的详细日志
4. **监控告警**: 设置API调用失败的告警机制

---

**最后更新**: 2025年1月15日  
**版本**: v1.0  
**维护者**: YUE哥团队
