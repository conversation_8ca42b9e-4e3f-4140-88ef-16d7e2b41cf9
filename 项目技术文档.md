# 智能票据处理系统 - 完整技术文档

## 🎯 项目概述

一个基于AI和PyQt5开发的智能票据处理系统，能够自动识别和解析票据图像，提取商品信息，并与ERP系统集成进行成本价管理和利润分析。系统采用模块化设计，具备现代化界面和完整的状态管理机制。

### 🌟 核心特性
- **AI智能解析**：自动识别票据中的商品信息（款号、颜色规格、数量、单价等）
- **ERP系统集成**：实时查询ERP商品信息和价格数据，智能SKU匹配
- **成本价管理**：自动计算商品利润和毛利率，批量更新ERP系统成本价
- **现代化界面**：基于PyQt5的美观界面设计，黑色主题，响应式布局
- **数据状态管理**：完整的状态持久化和隔离机制，避免数据污染

---

## 🏗️ 技术架构

### 📂 项目结构
```
自动填写成本价/
├── 📄 main.py                          # 程序启动入口
├── 🚀 启动程序.bat                     # Windows快速启动脚本
├── 📋 requirements.txt                 # Python依赖包列表
├── 📄 项目技术文档.md                  # 本文档
├── 📄 ERP_API_文档.md                  # ERP API接口详细文档
│
├── 📂 modules/                         # 核心功能模块目录
│   ├── 🎨 pyqt5_main_gui.py           # 主界面入口（重构后轻量版）
│   ├── 🤖 ai_processor.py             # AI解析处理核心
│   ├── 🔍 erp_integration.py          # ERP系统集成接口
│   ├── 🎯 sku_matcher.py              # 智能SKU匹配算法
│   ├── ⚙️ config_manager.py           # 配置管理器
│   ├── 📁 file_manager.py             # 文件管理器
│   ├── 🖼️ image_splitter.py           # 图像分割处理
│   ├── 🏢 supplier_manager.py         # 供应商管理
│   ├── 🎨 color_confirm_panel.py      # 颜色确认面板
│   ├── 📊 table_manager.py            # 表格数据管理
│   │
│   ├── 📂 gui/                        # GUI界面模块
│   │   ├── 🏠 main_gui_controller.py  # 主界面控制器
│   │   ├── 🖼️ image_manager.py        # 图像管理器
│   │   ├── 📊 table_ui_manager.py     # 表格界面管理（⚠️ 核心模块）
│   │   ├── 🤖 ai_processor_ui.py      # AI处理界面
│   │   ├── ⚙️ config_ui_manager.py    # 配置界面管理
│   │   ├── 🔍 erp_ui_manager.py       # ERP界面管理
│   │   ├── 🎮 event_handlers.py       # 事件处理器
│   │   └── 🎴 image_card_widget.py    # 图像卡片组件
│   │
│   ├── 📂 threads/                    # 线程处理模块
│   │   ├── 🤖 ai_processing_thread.py # AI处理线程
│   │   ├── 🔍 erp_query_thread.py     # ERP查询线程
│   │   ├── 💰 cost_update_thread.py   # 成本价更新线程
│   │   └── 📝 thread_safe_logger.py   # 线程安全日志器
│   │
│   ├── 📂 cookies/                    # Cookie管理模块
│   │   ├── manager.py                 # Cookie管理器
│   │   ├── parser.py                  # Cookie解析器
│   │   ├── validator.py               # Cookie验证器
│   │   └── exceptions.py              # Cookie异常定义
│   │
│   └── 📂 utils/                      # 工具模块
│       └── 📝 logger.py               # 日志管理工具
│
├── 📂 beifen/                         # 备份文件存储目录
├── 📂 temp_segments/                  # 临时图像片段存储
│
├── 📄 user_config.json                # 用户个人配置文件
├── 📄 suppliers.json                  # 供应商数据库
├── 📄 file_manager.json               # 文件管理配置
└── 📄 latest_cookies.json             # ERP系统访问Cookie
```

### 🔧 核心模块说明

#### 1. 主界面控制器 (`main_gui_controller.py`)
- **职责**：主界面逻辑控制、事件协调
- **关键方法**：
  - `on_color_confirmed()` - 颜色确认回调处理
  - `_calculate_profit_for_confirmed_item()` - 利润计算和保存

#### 2. 表格界面管理器 (`table_ui_manager.py`) ⚠️ **重要模块**
- **职责**：表格数据管理、ERP状态管理、文件间状态隔离
- **关键方法**：
  - `save_current_erp_state()` - 保存当前ERP状态
  - `restore_erp_state_for_file()` - 恢复文件ERP状态
  - `restore_table_row_status_from_erp_results()` - 恢复表格行状态
- **核心功能**：解决表格数据污染问题

#### 3. ERP集成模块 (`erp_integration.py`)
- **职责**：ERP系统API调用、数据查询和更新
- **支持功能**：商品查询、成本价更新、库存状态查询

#### 4. AI处理器 (`ai_processor.py`)
- **职责**：票据图像识别、商品信息提取
- **支持模型**：doubao-1.5-vision-pro 等主流视觉模型

---

## 🚨 核心技术难点：表格数据污染解决方案

### 问题背景
在多文件票据处理场景下，系统存在严重的表格数据污染问题：
1. **状态污染**：查询第一个表格ERP后，切换到其他表格会显示第一个表格的状态
2. **状态丢失**：切换表格时已确认的颜色状态和利润数据会丢失
3. **数据混乱**：不同文件的商品数据互相干扰，导致错误的匹配结果

### 解决方案架构

#### 1. 状态隔离机制
**核心原理**：为每个文件独立维护ERP状态，确保文件间完全隔离。

```python
# 状态保存结构
erp_states_cache = {
    "file_path_1": {
        "sku_based_erp_state": {
            "商品A|红色XL": {
                "status": "📈 上涨 ¥5",
                "confirmed": True,
                "profit_text": "¥20",
                "color_groups": {...}
            }
        }
    },
    "file_path_2": {
        "sku_based_erp_state": {
            "商品B|蓝色M": {
                "status": "📉 下降 ¥3",
                "confirmed": True,
                "profit_text": "¥15"
            }
        }
    }
}
```

#### 2. 基于商品唯一标识的状态管理
**问题**：原系统使用表格行号作为状态标识，导致不同文件的相同行号混乱。

**解决方案**：使用 `"{款号}|{颜色规格}"` 作为商品唯一标识。

```python
def generate_sku_key(item_data):
    """生成商品唯一标识"""
    sku_code = item_data.get("款号", "").strip()
    color_spec = item_data.get("颜色规格", "").strip()
    return f"{sku_code}|{color_spec}"
```

#### 3. 状态切换时的清空和恢复机制
**核心逻辑**：无论目标文件是否有保存状态，都要先清空全局状态，然后根据情况恢复。

```python
def restore_erp_state_for_file(self, file_path):
    """恢复指定文件的ERP状态 - 确保状态隔离"""
    
    # 🔥 核心：先清空当前ERP状态，确保状态隔离
    if not hasattr(self.main_window, 'erp_match_results'):
        self.main_window.erp_match_results = {}
    
    print(f"🧹 清空当前ERP状态，准备切换到文件: {os.path.basename(file_path)}")
    self.main_window.erp_match_results.clear()
    
    # 检查目标文件是否有保存的状态
    if file_path not in self.main_window.erp_states_cache:
        print(f"🔄 文件没有保存的ERP状态，显示空状态")
        self.restore_table_row_status_from_erp_results()
        return
    
    # 恢复保存的状态...
```

#### 4. 利润数据完整保存和恢复
**问题**：确认颜色后的利润数据没有保存到ERP状态中。

**解决方案**：在利润计算时同步保存到状态缓存。

```python
def _calculate_profit_for_confirmed_item(self, table, row, selected_skus, ticket_price):
    """计算确认商品的利润并保存到状态"""
    # ... 利润计算逻辑 ...
    
    # 🔥 核心修复：将利润数据保存到erp_match_results中
    if hasattr(self, 'erp_match_results') and row in self.erp_match_results:
        self.erp_match_results[row]["profit_text"] = profit_text
        self.erp_match_results[row]["profit_value"] = profit
        self.erp_match_results[row]["sale_price"] = sale_price_float
        self.erp_match_results[row]["cost_price"] = cost_price_float
```

#### 5. 完整的状态恢复逻辑
**关键点**：恢复时要同时处理状态显示和利润数据。

```python
def restore_table_row_status_from_erp_results(self):
    """根据ERP查询结果恢复表格行状态"""
    for row, match_result in self.main_window.erp_match_results.items():
        # 恢复状态显示
        saved_status = match_result.get('status', '')
        confirmed = match_result.get('confirmed', False)
        
        if saved_status and confirmed:
            erp_status = saved_status  # 使用确认后的状态
        elif color_groups:
            erp_status = "🤔 待确认"  # 显示待确认
        else:
            erp_status = "✅ 已查询"  # 默认状态
            
        # 🔥 核心修复：同时恢复利润数据
        profit_text = match_result.get('profit_text', '')
        if profit_text and confirmed:
            profit_item = QTableWidgetItem(profit_text)
            # 设置颜色和格式...
            current_table.setItem(row, 7, profit_item)
```

### 技术要点总结

#### ✅ 已解决的核心问题
1. **状态污染**：完全的文件间状态隔离
2. **状态丢失**：确认后的状态和利润数据持久化
3. **数据混乱**：基于商品唯一标识的精确匹配
4. **意外清空**：条件性状态清空机制
5. **路径错误**：准确的文件路径追踪

#### ⚠️ 关键注意事项
1. **状态保存时机**：在颜色确认和利润计算时立即保存
2. **状态恢复条件**：只有 `confirmed=True` 的商品才恢复完整状态
3. **错误处理**：出错时保证空状态而非脏状态
4. **兼容性**：对旧格式数据的兼容性处理
5. **调试支持**：详细的状态切换日志

---

## 🔧 系统配置

### 1. 运行环境要求
- **操作系统**: Windows 10/11
- **Python版本**: 3.8 或更高版本
- **内存**: 至少 4GB RAM
- **网络**: 稳定的互联网连接

### 2. 依赖安装
```bash
pip install -r requirements.txt
```

### 3. 关键配置文件

#### AI服务配置 (`user_config.json`)
```json
{
  "ai": {
    "api_key": "your-api-key-here",
    "api_url": "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
    "model_name": "doubao-1.5-vision-pro",
    "temperature": 70,
    "max_tokens": 4000,
    "timeout": 60
  }
}
```

#### ERP访问配置 (`latest_cookies.json`)
```json
{
  "cookies": {
    "session_id": "your-session-id",
    "auth_token": "your-auth-token"
  }
}
```

---

## 🎯 使用流程

### 标准操作流程
1. **图像处理** → 选择文件/粘贴图像 → 预览和分割
2. **AI解析** → 启动解析 → 查看解析结果 → 验证数据
3. **ERP查询** → 商品查询 → SKU匹配 → 价格对比
4. **颜色确认** → 多规格商品选择颜色 → 确认状态更新
5. **成本价更新** → 利润分析 → 选择更新 → 执行更新

### 多文件处理要点
- 每个文件的状态完全独立，支持无限制切换
- 确认后的状态和利润会自动保存和恢复
- 支持批量处理多张票据

---

## 🐛 故障排除

### 常见问题及解决方案

#### 1. ERP状态显示异常
**现象**：切换文件时状态显示错误或丢失
**解决**：检查 `table_ui_manager.py` 中的状态管理逻辑
**日志关键字**：`🧹 清空当前ERP状态`、`💰 恢复利润`

#### 2. Cookie失效问题
**现象**：ERP查询失败，提示认证错误
**解决**：更新 `latest_cookies.json` 中的Cookie信息
**获取方式**：浏览器开发者工具 → Network → 复制Cookie

#### 3. AI解析失败
**现象**：图像上传后无法识别内容
**解决**：检查API配置、网络连接、图像质量
**日志关键字**：`AI处理失败`、`API调用错误`

#### 4. 图像预览问题
**现象**：全屏预览无法正常切换或显示
**解决**：检查图像文件路径和缩略图生成
**相关模块**：`image_manager.py`

---

## 🔍 维护指南

### 代码维护要点

#### 1. 状态管理模块 (`table_ui_manager.py`)
- **核心方法**：不要轻易修改状态保存和恢复的核心逻辑
- **扩展方式**：在现有基础上添加新的状态字段
- **调试方法**：启用详细日志，观察状态切换过程

#### 2. ERP集成模块 (`erp_integration.py`)
- **API变更**：ERP系统升级时需要更新API接口
- **错误处理**：完善网络异常和业务异常的处理
- **性能优化**：考虑请求缓存和批量操作

#### 3. 界面模块 (`gui/`)
- **组件复用**：新增界面组件时注意与现有组件的一致性
- **事件处理**：确保事件处理的线程安全
- **样式管理**：统一使用 `ui_style_manager.py` 管理样式

### 性能优化建议

#### 1. 图像处理优化
- 大图像分割前进行压缩
- 缓存缩略图避免重复生成
- 异步处理图像上传和识别

#### 2. ERP查询优化
- 批量查询减少API调用次数
- 实现查询结果缓存机制
- 支持增量更新减少数据传输

#### 3. 界面响应优化
- 耗时操作使用线程处理
- 大数据表格使用虚拟滚动
- 合理设置界面更新频率

---

## 📝 更新日志

### 最新版本功能
- ✅ 完整的表格数据状态管理和隔离机制
- ✅ 基于商品唯一标识的精确状态匹配
- ✅ 颜色确认状态和利润数据的持久化
- ✅ 全屏图像预览和智能导航功能
- ✅ Cookie管理和ERP认证优化
- ✅ 现代化界面设计和用户体验

### 技术债务清理
- 🔄 重构了原始的大文件 `pyqt5_main_gui.py`
- 🔄 模块化了各功能组件，提高可维护性
- 🔄 统一了日志记录和错误处理机制
- 🔄 优化了线程管理和资源释放

---

## 🤝 开发规范

### 代码规范
- 使用类型提示提高代码可读性
- 重要方法添加详细的文档字符串
- 关键操作添加调试日志
- 异常处理要具体和有意义

### 提交规范
- 功能修改要有对应的测试验证
- 重要修改要更新相关文档
- 备份关键文件到 `beifen/` 目录
- 修改说明要包含问题背景和解决方案

### 测试规范
- 新功能要测试多种边界情况
- 表格状态管理要测试文件切换场景
- ERP集成要测试网络异常情况
- 界面操作要测试用户交互流程

---

**最后更新时间**: 2024-12-21  
**文档版本**: v1.0  
**维护人员**: 一介哥开发团队 