#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试布局优化功能
验证左侧面板固定宽度和表格列宽设置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.ui_constants import UIConstants

def test_column_widths():
    """测试表格列宽设置"""
    print("🧪 测试表格列宽设置")
    print("=" * 50)
    
    # 验证新的列宽常量
    print(f"📏 新的列宽常量:")
    print(f"   - COLUMN_WIDTH_TINY: {UIConstants.Table.COLUMN_WIDTH_TINY}px (行数列)")
    print(f"   - COLUMN_WIDTH_SMALL: {UIConstants.Table.COLUMN_WIDTH_SMALL}px (退货率列)")
    print(f"   - COLUMN_WIDTH_MEDIUM: {UIConstants.Table.COLUMN_WIDTH_MEDIUM}px (数量等列)")
    print(f"   - COLUMN_WIDTH_LARGE: {UIConstants.Table.COLUMN_WIDTH_LARGE}px (款号列)")
    print(f"   - COLUMN_WIDTH_XLARGE: {UIConstants.Table.COLUMN_WIDTH_XLARGE}px (颜色规格列)")
    print(f"   - COLUMN_WIDTH_STATUS: {UIConstants.Table.COLUMN_WIDTH_STATUS}px (状态列)")
    
    # 计算总宽度
    column_widths = [
        UIConstants.Table.COLUMN_WIDTH_TINY,    # 行数 (40px)
        UIConstants.Table.COLUMN_WIDTH_LARGE,   # 款号 (120px)
        UIConstants.Table.COLUMN_WIDTH_XLARGE,  # 颜色规格 (140px)
        UIConstants.Table.COLUMN_WIDTH_MEDIUM,  # 数量 (80px)
        UIConstants.Table.COLUMN_WIDTH_MEDIUM,  # 单价 (80px)
        UIConstants.Table.COLUMN_WIDTH_MEDIUM,  # 小计 (80px)
        UIConstants.Table.COLUMN_WIDTH_STATUS,  # 状态 (150px)
        UIConstants.Table.COLUMN_WIDTH_MEDIUM,  # 利润 (80px)
        UIConstants.Table.COLUMN_WIDTH_SMALL,   # 7退 (70px)
        UIConstants.Table.COLUMN_WIDTH_SMALL,   # 15退 (70px)
        UIConstants.Table.COLUMN_WIDTH_SMALL,   # 30退 (70px)
    ]
    
    total_width = sum(column_widths)
    
    print(f"\n📊 列宽分配详情:")
    column_names = ["行数", "款号", "颜色规格", "数量", "单价", "小计", "状态", "利润", "7退", "15退", "30退"]
    for i, (name, width) in enumerate(zip(column_names, column_widths)):
        print(f"   {i+1:2d}. {name:8s}: {width:3d}px")
    
    print(f"\n📏 总宽度: {total_width}px")
    
    # 验证是否符合要求
    if total_width <= 980:
        print(f"✅ 总宽度 {total_width}px ≤ 980px，符合要求！")
    else:
        print(f"❌ 总宽度 {total_width}px > 980px，超出限制！")
    
    # 验证行数列宽度
    if UIConstants.Table.COLUMN_WIDTH_TINY == 40:
        print(f"✅ 行数列宽度 {UIConstants.Table.COLUMN_WIDTH_TINY}px = 40px，符合要求！")
    else:
        print(f"❌ 行数列宽度 {UIConstants.Table.COLUMN_WIDTH_TINY}px ≠ 40px，不符合要求！")
    
    return total_width <= 980 and UIConstants.Table.COLUMN_WIDTH_TINY == 40

def test_splitter_sizes():
    """测试分割器尺寸设置"""
    print("\n🧪 测试分割器尺寸设置")
    print("=" * 50)
    
    print(f"📏 主分割器尺寸: {UIConstants.MAIN_SPLITTER_SIZES}")
    
    left_width = UIConstants.MAIN_SPLITTER_SIZES[0]
    center_width = UIConstants.MAIN_SPLITTER_SIZES[1]
    right_width = UIConstants.MAIN_SPLITTER_SIZES[2]
    
    print(f"   - 左侧面板: {left_width}px")
    print(f"   - 中间表格: {center_width}px")
    print(f"   - 右侧面板: {right_width}px")
    
    # 验证左侧面板是否为700px
    if left_width == 700:
        print(f"✅ 左侧面板宽度 {left_width}px = 700px，符合要求！")
        return True
    else:
        print(f"❌ 左侧面板宽度 {left_width}px ≠ 700px，不符合要求！")
        return False

def test_table_headers():
    """测试表格列头设置"""
    print("\n🧪 测试表格列头设置")
    print("=" * 50)
    
    pickup_headers = UIConstants.Table.PICKUP_HEADERS
    return_headers = UIConstants.Table.RETURN_HEADERS
    
    print(f"📋 拿货表格列头 ({len(pickup_headers)}列):")
    for i, header in enumerate(pickup_headers):
        print(f"   {i+1:2d}. {header}")
    
    print(f"\n📋 退货表格列头 ({len(return_headers)}列):")
    for i, header in enumerate(return_headers):
        print(f"   {i+1:2d}. {header}")
    
    # 验证列数
    expected_columns = 11
    pickup_ok = len(pickup_headers) == expected_columns
    return_ok = len(return_headers) == expected_columns
    
    if pickup_ok and return_ok:
        print(f"✅ 表格列数 {len(pickup_headers)}列 = {expected_columns}列，符合要求！")
    else:
        print(f"❌ 表格列数不符合要求！预期{expected_columns}列")
    
    # 验证退货率列头
    expected_return_headers = ["7退", "15退", "30退"]
    actual_return_headers = pickup_headers[-3:]
    
    if actual_return_headers == expected_return_headers:
        print(f"✅ 退货率列头 {actual_return_headers} 符合要求！")
        return True
    else:
        print(f"❌ 退货率列头 {actual_return_headers} ≠ {expected_return_headers}，不符合要求！")
        return False

if __name__ == "__main__":
    print("🚀 开始测试布局优化功能")
    
    try:
        # 运行所有测试
        test1_result = test_column_widths()
        test2_result = test_splitter_sizes()
        test3_result = test_table_headers()
        
        print("\n" + "=" * 50)
        print("📊 测试结果汇总:")
        print(f"   - 表格列宽测试: {'✅ 通过' if test1_result else '❌ 失败'}")
        print(f"   - 分割器尺寸测试: {'✅ 通过' if test2_result else '❌ 失败'}")
        print(f"   - 表格列头测试: {'✅ 通过' if test3_result else '❌ 失败'}")
        
        if all([test1_result, test2_result, test3_result]):
            print("\n🎉 所有测试通过！布局优化配置正确！")
        else:
            print("\n❌ 部分测试失败，需要检查配置！")
            
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
